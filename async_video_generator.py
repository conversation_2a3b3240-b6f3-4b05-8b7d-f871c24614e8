import os
import asyncio
import base64
import mimetypes
import aiofiles
from pathlib import Path
from typing import Optional, List, Dict, Any
from dataclasses import dataclass
from dotenv import load_dotenv
# 通过 pip install 'volcengine-python-sdk[ark]' 安装方舟SDK
from volcenginesdkarkruntime import Ark

# 重用现有的数据类
from video_generator import VideoGenerationConfig, TaskResult


class AsyncVideoGenerator:
    """异步视频生成处理类
    
    这个类提供了VideoGenerator的异步版本，支持：
    - 异步创建视频生成任务
    - 异步轮询任务状态
    - 异步获取任务详情
    - 异步列出任务
    - 异步删除任务
    - 异步文件处理
    - 并发任务处理
    """
    
    def __init__(self, base_url: Optional[str] = None, api_key: Optional[str] = None,
                 default_config: Optional[VideoGenerationConfig] = None):
        """初始化异步视频生成器

        Args:
            base_url: API基础URL，如果为None则从环境变量ARK_BASE_URL读取
            api_key: API密钥，如果为None则从环境变量ARK_API_KEY读取
            default_config: 默认配置，如果为None则自动从环境变量加载
        """
        load_dotenv()

        self.client = Ark(
            base_url=base_url or os.environ.get("ARK_BASE_URL"),
            api_key=api_key or os.environ.get("ARK_API_KEY"),
        )

        # 自动加载默认配置
        if default_config is None:
            self.default_config = VideoGenerationConfig.from_env()
        else:
            self.default_config = default_config

        # 保持向后兼容
        self.default_model = self.default_config.model
    
    async def create_video_task(self,
                               prompt: str,
                               config: Optional[VideoGenerationConfig] = None,
                               image_url: Optional[str] = None,
                               first_frame_url: Optional[str] = None,
                               last_frame_url: Optional[str] = None) -> TaskResult:
        """异步创建视频生成任务

        Args:
            prompt: 文本提示词，描述要生成的视频内容
            config: 视频生成配置，如果为None则使用默认配置
            image_url: 可选的首帧图片URL或本地文件路径，用于图生视频（兼容旧接口）
                      支持网络URL (http://..., https://...) 和本地文件路径
                      本地文件会自动转换为base64编码
            first_frame_url: 可选的首帧图片URL或本地文件路径，用于首尾帧视频生成
            last_frame_url: 可选的尾帧图片URL或本地文件路径，用于首尾帧视频生成

        Returns:
            TaskResult: 包含任务ID和初始状态的结果对象

        Raises:
            Exception: 当API调用失败时抛出异常
        """
        if config is None:
            config = self.default_config
        
        try:
            # 构建完整的提示词，包含视频参数
            full_prompt = self._build_prompt_with_params(prompt, config)
            
            # 构建内容列表
            content = [
                {
                    "type": "text",
                    "text": full_prompt
                }
            ]
            
            # 处理图片输入（支持多种模式）
            await self._add_image_content(content, image_url, first_frame_url, last_frame_url)
            
            # 构建请求参数
            request_params = {
                "model": config.model,
                "content": content
            }
            
            # 使用asyncio.to_thread包装同步API调用
            result = await asyncio.to_thread(
                self.client.content_generation.tasks.create, 
                **request_params
            )
            
            return TaskResult(
                task_id=result.id,
                status=result.status if hasattr(result, 'status') else 'created',
                model=config.model
            )
            
        except Exception as e:
            raise Exception(f"创建视频生成任务失败: {str(e)}")
    
    def _build_prompt_with_params(self, prompt: str, config: VideoGenerationConfig) -> str:
        """构建包含参数的完整提示词
        
        Args:
            prompt: 基础提示词
            config: 视频生成配置
            
        Returns:
            str: 包含参数的完整提示词
        """
        # 基础提示词
        full_prompt = prompt
        
        # 添加参数到提示词中
        params = []
        
        if config.resolution:
            params.append(f"--rs {config.resolution}")
        
        if config.ratio:
            params.append(f"--rt {config.ratio}")
        
        if config.duration:
            params.append(f"--dur {config.duration}")
        
        if config.framespersecond:
            params.append(f"--fps {config.framespersecond}")
        
        if config.seed is not None:
            params.append(f"--seed {config.seed}")
        
        # 将参数添加到提示词末尾
        if params:
            full_prompt += " " + " ".join(params)
        
        return full_prompt
    
    async def _process_image_input_async(self, image_input: str) -> str:
        """异步处理图片输入，支持URL和本地文件路径
        
        Args:
            image_input: 图片URL或本地文件路径
            
        Returns:
            str: 处理后的图片URL（网络URL或base64 data URL）
            
        Raises:
            Exception: 当文件不存在或格式不支持时抛出异常
        """
        # 检查是否是网络URL
        if image_input.startswith(('http://', 'https://')):
            return image_input
        
        # 处理本地文件
        file_path = Path(image_input)
        
        if not file_path.exists():
            raise Exception(f"图片文件不存在: {image_input}")
        
        if not file_path.is_file():
            raise Exception(f"路径不是文件: {image_input}")
        
        # 检查文件类型
        mime_type, _ = mimetypes.guess_type(str(file_path))
        if not mime_type or not mime_type.startswith('image/'):
            raise Exception(f"不支持的图片格式: {image_input}")
        
        try:
            # 异步读取文件并转换为base64
            async with aiofiles.open(file_path, 'rb') as f:
                image_data = await f.read()
            
            # 编码为base64
            base64_data = base64.b64encode(image_data).decode('utf-8')
            
            # 构建data URL
            data_url = f"data:{mime_type};base64,{base64_data}"
            
            return data_url
            
        except Exception as e:
            raise Exception(f"读取图片文件失败: {str(e)}")

    async def _add_image_content(self,
                                content: List[Dict[str, Any]],
                                image_url: Optional[str] = None,
                                first_frame_url: Optional[str] = None,
                                last_frame_url: Optional[str] = None) -> None:
        """添加图片内容到请求中

        Args:
            content: 内容列表，将在此基础上添加图片
            image_url: 兼容旧接口的图片URL（作为首帧）
            first_frame_url: 首帧图片URL
            last_frame_url: 尾帧图片URL
        """
        # 处理兼容性：如果使用了旧的image_url参数，将其作为首帧
        if image_url and not first_frame_url:
            first_frame_url = image_url

        # 添加首帧图片
        if first_frame_url:
            processed_first_frame = await self._process_image_input_async(first_frame_url)
            content.append({
                "type": "image_url",
                "image_url": {
                    "url": processed_first_frame
                },
                "role": "first_frame"
            })

        # 添加尾帧图片
        if last_frame_url:
            processed_last_frame = await self._process_image_input_async(last_frame_url)
            content.append({
                "type": "image_url",
                "image_url": {
                    "url": processed_last_frame
                },
                "role": "last_frame"
            })

    async def get_task_status(self, task_id: str) -> TaskResult:
        """异步获取任务状态和详情
        
        Args:
            task_id: 任务ID
            
        Returns:
            TaskResult: 包含任务详细信息的结果对象
            
        Raises:
            Exception: 当API调用失败时抛出异常
        """
        try:
            # 使用asyncio.to_thread包装同步API调用
            result = await asyncio.to_thread(
                self.client.content_generation.tasks.get,
                task_id=task_id
            )
            
            return TaskResult(
                task_id=result.id,
                status=result.status,
                model=getattr(result, 'model', None),
                video_url=result.content.video_url if result.content else None,
                error=result.error,
                usage=result.usage.__dict__ if result.usage else None,
                created_at=result.created_at,
                updated_at=result.updated_at,
                seed=result.seed,
                revised_prompt=result.revised_prompt
            )
            
        except Exception as e:
            raise Exception(f"获取任务状态失败: {str(e)}")
    
    async def wait_for_completion(self, 
                                 task_id: str, 
                                 poll_interval: int = 10,
                                 max_wait_time: int = 600,
                                 verbose: bool = True) -> TaskResult:
        """异步等待任务完成
        
        Args:
            task_id: 任务ID
            poll_interval: 轮询间隔（秒），默认10秒
            max_wait_time: 最大等待时间（秒），默认600秒（10分钟）
            verbose: 是否打印轮询状态，默认True
            
        Returns:
            TaskResult: 最终的任务结果
            
        Raises:
            Exception: 当任务失败或超时时抛出异常
        """
        start_time = asyncio.get_event_loop().time()
        
        while True:
            # 检查是否超时
            if asyncio.get_event_loop().time() - start_time > max_wait_time:
                raise Exception(f"任务等待超时，超过{max_wait_time}秒")
            
            result = await self.get_task_status(task_id)
            
            if result.status == "succeeded":
                if verbose:
                    print("任务成功完成！")
                return result
            elif result.status == "failed":
                error_msg = result.error or "未知错误"
                raise Exception(f"任务失败: {error_msg}")
            elif result.status in ["cancelled"]:
                raise Exception(f"任务被取消")
            else:
                if verbose:
                    print(f"当前状态: {result.status}, {poll_interval}秒后重试...")
                await asyncio.sleep(poll_interval)  # 关键：异步等待
    
    async def list_tasks(self, 
                        page_num: int = 1,
                        page_size: int = 10,
                        status: Optional[str] = None,
                        model: Optional[str] = None,
                        task_ids: Optional[List[str]] = None) -> Dict[str, Any]:
        """异步列出任务
        
        Args:
            page_num: 页码，默认1
            page_size: 每页大小，默认10
            status: 按状态筛选，可选值：queued, running, succeeded, failed, cancelled
            model: 按模型筛选
            task_ids: 按任务ID列表筛选
            
        Returns:
            Dict: 包含任务列表和总数的字典
            
        Raises:
            Exception: 当API调用失败时抛出异常
        """
        try:
            params = {
                "page_num": page_num,
                "page_size": page_size
            }
            
            if status:
                params["status"] = status
            if model:
                params["model"] = model
            if task_ids:
                params["task_ids"] = task_ids
            
            # 使用asyncio.to_thread包装同步API调用
            result = await asyncio.to_thread(
                self.client.content_generation.tasks.list,
                **params
            )
            
            return {
                "total": result.total,
                "items": [
                    {
                        "task_id": item.id,
                        "status": item.status,
                        "model": item.model,
                        "created_at": item.created_at,
                        "updated_at": item.updated_at
                    }
                    for item in result.items
                ]
            }
            
        except Exception as e:
            raise Exception(f"列出任务失败: {str(e)}")
    
    async def delete_task(self, task_id: str) -> bool:
        """异步删除任务
        
        Args:
            task_id: 要删除的任务ID
            
        Returns:
            bool: 删除是否成功
            
        Raises:
            Exception: 当API调用失败时抛出异常
        """
        try:
            # 使用asyncio.to_thread包装同步API调用
            await asyncio.to_thread(
                self.client.content_generation.tasks.delete,
                task_id=task_id
            )
            return True
        except Exception as e:
            raise Exception(f"删除任务失败: {str(e)}")

    async def generate_video(self,
                            prompt: str,
                            config: Optional[VideoGenerationConfig] = None,
                            image_url: Optional[str] = None,
                            first_frame_url: Optional[str] = None,
                            last_frame_url: Optional[str] = None,
                            wait_for_completion: bool = True,
                            poll_interval: int = 10,
                            max_wait_time: int = 600,
                            verbose: bool = True) -> TaskResult:
        """异步一站式视频生成方法

        这个方法将创建任务和等待完成合并为一个操作，简化使用流程。
        支持文生视频、图生视频和首尾帧视频生成三种模式。

        Args:
            prompt: 文本提示词
            config: 视频生成配置
            image_url: 可选的首帧图片URL，用于图生视频（兼容旧接口）
            first_frame_url: 可选的首帧图片URL，用于首尾帧视频生成
            last_frame_url: 可选的尾帧图片URL，用于首尾帧视频生成
            wait_for_completion: 是否等待任务完成，默认True
            poll_interval: 轮询间隔（秒）
            max_wait_time: 最大等待时间（秒）
            verbose: 是否打印详细信息

        Returns:
            TaskResult: 任务结果，如果wait_for_completion为True则返回最终结果
        """
        if verbose:
            # 确定视频生成类型
            if first_frame_url and last_frame_url:
                video_type = "首尾帧视频"
                print(f"开始生成{video_type}: {prompt}")
                print(f"首帧图片: {first_frame_url}")
                print(f"尾帧图片: {last_frame_url}")
            elif image_url or first_frame_url:
                video_type = "图生视频"
                print(f"开始生成{video_type}: {prompt}")
                print(f"首帧图片: {image_url or first_frame_url}")
            else:
                video_type = "文生视频"
                print(f"开始生成{video_type}: {prompt}")

        # 创建任务
        result = await self.create_video_task(prompt, config, image_url, first_frame_url, last_frame_url)

        if verbose:
            print(f"任务已创建，ID: {result.task_id}")

        if wait_for_completion:
            # 等待完成
            result = await self.wait_for_completion(
                result.task_id,
                poll_interval=poll_interval,
                max_wait_time=max_wait_time,
                verbose=verbose
            )

            if verbose and result.video_url:
                print(f"视频生成完成！下载链接: {result.video_url}")

        return result

    async def generate_text_to_video(self,
                                    prompt: str,
                                    config: Optional[VideoGenerationConfig] = None,
                                    wait_for_completion: bool = True,
                                    poll_interval: int = 10,
                                    max_wait_time: int = 600,
                                    verbose: bool = True) -> TaskResult:
        """异步文生视频专用方法

        Args:
            prompt: 文本提示词
            config: 视频生成配置
            wait_for_completion: 是否等待任务完成
            poll_interval: 轮询间隔（秒）
            max_wait_time: 最大等待时间（秒）
            verbose: 是否打印详细信息

        Returns:
            TaskResult: 任务结果
        """
        return await self.generate_video(
            prompt=prompt,
            config=config,
            image_url=None,
            wait_for_completion=wait_for_completion,
            poll_interval=poll_interval,
            max_wait_time=max_wait_time,
            verbose=verbose
        )

    async def generate_image_to_video(self,
                                     prompt: str,
                                     image_url: str,
                                     config: Optional[VideoGenerationConfig] = None,
                                     wait_for_completion: bool = True,
                                     poll_interval: int = 10,
                                     max_wait_time: int = 600,
                                     verbose: bool = True) -> TaskResult:
        """异步图生视频专用方法

        Args:
            prompt: 文本提示词，描述视频中的动作和变化
            image_url: 首帧图片的URL或本地文件路径
            config: 视频生成配置
            wait_for_completion: 是否等待任务完成
            poll_interval: 轮询间隔（秒）
            max_wait_time: 最大等待时间（秒）
            verbose: 是否打印详细信息

        Returns:
            TaskResult: 任务结果
        """
        if not image_url:
            raise ValueError("图生视频必须提供image_url参数")

        return await self.generate_video(
            prompt=prompt,
            config=config,
            image_url=image_url,
            wait_for_completion=wait_for_completion,
            poll_interval=poll_interval,
            max_wait_time=max_wait_time,
            verbose=verbose
        )

    async def generate_first_last_frame_video(self,
                                             prompt: str,
                                             first_frame_url: str,
                                             last_frame_url: str,
                                             config: Optional[VideoGenerationConfig] = None,
                                             wait_for_completion: bool = True,
                                             poll_interval: int = 10,
                                             max_wait_time: int = 600,
                                             verbose: bool = True) -> TaskResult:
        """异步首尾帧视频生成专用方法

        Args:
            prompt: 文本提示词，描述视频中的动作和变化
            first_frame_url: 首帧图片的URL或本地文件路径
            last_frame_url: 尾帧图片的URL或本地文件路径
            config: 视频生成配置
            wait_for_completion: 是否等待任务完成
            poll_interval: 轮询间隔（秒）
            max_wait_time: 最大等待时间（秒）
            verbose: 是否打印详细信息

        Returns:
            TaskResult: 任务结果
        """
        if not first_frame_url or not last_frame_url:
            raise ValueError("首尾帧视频生成必须同时提供first_frame_url和last_frame_url参数")

        return await self.generate_video(
            prompt=prompt,
            config=config,
            first_frame_url=first_frame_url,
            last_frame_url=last_frame_url,
            wait_for_completion=wait_for_completion,
            poll_interval=poll_interval,
            max_wait_time=max_wait_time,
            verbose=verbose
        )

    # 并发处理方法
    async def generate_videos_batch(self,
                                   prompts: List[str],
                                   configs: Optional[List[VideoGenerationConfig]] = None,
                                   image_urls: Optional[List[Optional[str]]] = None,
                                   first_frame_urls: Optional[List[Optional[str]]] = None,
                                   last_frame_urls: Optional[List[Optional[str]]] = None,
                                   max_concurrent: int = 3,
                                   poll_interval: int = 10,
                                   max_wait_time: int = 600,
                                   verbose: bool = True) -> List[TaskResult]:
        """异步批量生成视频

        Args:
            prompts: 文本提示词列表
            configs: 配置列表，如果为None则使用默认配置
            image_urls: 图片URL列表，用于图生视频
            first_frame_urls: 首帧图片URL列表，用于首尾帧视频生成
            last_frame_urls: 尾帧图片URL列表，用于首尾帧视频生成
            max_concurrent: 最大并发数，默认3
            poll_interval: 轮询间隔（秒）
            max_wait_time: 最大等待时间（秒）
            verbose: 是否打印详细信息

        Returns:
            List[TaskResult]: 任务结果列表
        """
        if configs is None:
            configs = [None] * len(prompts)
        if image_urls is None:
            image_urls = [None] * len(prompts)
        if first_frame_urls is None:
            first_frame_urls = [None] * len(prompts)
        if last_frame_urls is None:
            last_frame_urls = [None] * len(prompts)

        # 确保列表长度一致
        if len(configs) != len(prompts):
            configs = configs[:len(prompts)] + [None] * (len(prompts) - len(configs))
        if len(image_urls) != len(prompts):
            image_urls = image_urls[:len(prompts)] + [None] * (len(prompts) - len(image_urls))
        if len(first_frame_urls) != len(prompts):
            first_frame_urls = first_frame_urls[:len(prompts)] + [None] * (len(prompts) - len(first_frame_urls))
        if len(last_frame_urls) != len(prompts):
            last_frame_urls = last_frame_urls[:len(prompts)] + [None] * (len(prompts) - len(last_frame_urls))

        if verbose:
            print(f"开始批量生成 {len(prompts)} 个视频，最大并发数: {max_concurrent}")

        # 使用信号量控制并发数
        semaphore = asyncio.Semaphore(max_concurrent)

        async def generate_single(i: int, prompt: str, config: Optional[VideoGenerationConfig],
                                 image_url: Optional[str], first_frame_url: Optional[str], last_frame_url: Optional[str]):
            async with semaphore:
                if verbose:
                    print(f"开始生成视频 {i+1}/{len(prompts)}: {prompt[:50]}...")
                try:
                    result = await self.generate_video(
                        prompt=prompt,
                        config=config,
                        image_url=image_url,
                        first_frame_url=first_frame_url,
                        last_frame_url=last_frame_url,
                        wait_for_completion=True,
                        poll_interval=poll_interval,
                        max_wait_time=max_wait_time,
                        verbose=False  # 避免过多输出
                    )
                    if verbose:
                        print(f"✅ 视频 {i+1} 生成完成")
                    return result
                except Exception as e:
                    if verbose:
                        print(f"❌ 视频 {i+1} 生成失败: {e}")
                    raise

        # 并发执行所有任务
        tasks = [
            generate_single(i, prompt, config, image_url, first_frame_url, last_frame_url)
            for i, (prompt, config, image_url, first_frame_url, last_frame_url) in enumerate(
                zip(prompts, configs, image_urls, first_frame_urls, last_frame_urls)
            )
        ]

        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        successful_results = []
        failed_count = 0

        for i, result in enumerate(results):
            if isinstance(result, Exception):
                failed_count += 1
                if verbose:
                    print(f"任务 {i+1} 失败: {result}")
            else:
                successful_results.append(result)

        if verbose:
            print(f"批量生成完成！成功: {len(successful_results)}, 失败: {failed_count}")

        return successful_results

    async def wait_for_multiple_tasks(self,
                                     task_ids: List[str],
                                     poll_interval: int = 10,
                                     max_wait_time: int = 600,
                                     verbose: bool = True) -> List[TaskResult]:
        """异步等待多个任务完成

        Args:
            task_ids: 任务ID列表
            poll_interval: 轮询间隔（秒）
            max_wait_time: 最大等待时间（秒）
            verbose: 是否打印详细信息

        Returns:
            List[TaskResult]: 完成的任务结果列表
        """
        if verbose:
            print(f"等待 {len(task_ids)} 个任务完成...")

        # 并发等待所有任务
        tasks = [
            self.wait_for_completion(
                task_id,
                poll_interval=poll_interval,
                max_wait_time=max_wait_time,
                verbose=False  # 避免过多输出
            )
            for task_id in task_ids
        ]

        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        successful_results = []
        failed_count = 0

        for i, result in enumerate(results):
            if isinstance(result, Exception):
                failed_count += 1
                if verbose:
                    print(f"任务 {task_ids[i]} 失败: {result}")
            else:
                successful_results.append(result)
                if verbose:
                    print(f"✅ 任务 {task_ids[i]} 完成")

        if verbose:
            print(f"等待完成！成功: {len(successful_results)}, 失败: {failed_count}")

        return successful_results
