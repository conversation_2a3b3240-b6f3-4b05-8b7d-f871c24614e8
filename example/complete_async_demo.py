#!/usr/bin/env python3
"""
完整的异步视频生成器演示脚本

展示AsyncVideoGenerator的所有功能：
- 文生视频
- 图生视频（网络URL + 本地文件）
- 首尾帧视频生成
- 并发批量生成
- 混合模式生成
- 任务管理
"""

import asyncio
import os
import aiohttp
from dotenv import load_dotenv
from async_video_generator import AsyncVideoGenerator
from video_generator import VideoGenerationConfig

# 确保加载环境变量
load_dotenv()


async def download_sample_images():
    """下载示例图片用于演示"""
    print("📥 下载示例图片...")
    
    images = {
        "single_frame": {
            "url": "https://ark-project.tos-cn-beijing.volces.com/doc_image/i2v_foxrgirl.png",
            "path": "demo_single_frame.png"
        },
        "first_frame": {
            "url": "https://ark-project.tos-cn-beijing.volces.com/doc_image/wan_input_first_frame.png",
            "path": "demo_first_frame.png"
        },
        "last_frame": {
            "url": "https://ark-project.tos-cn-beijing.volces.com/doc_image/wan_input_last_frame.png",
            "path": "demo_last_frame.png"
        }
    }
    
    downloaded_paths = {}
    
    async with aiohttp.ClientSession() as session:
        for image_type, info in images.items():
            if os.path.exists(info["path"]):
                print(f"✅ {image_type} 图片已存在: {info['path']}")
                downloaded_paths[image_type] = info["path"]
                continue
            
            try:
                async with session.get(info["url"]) as response:
                    response.raise_for_status()
                    
                    with open(info["path"], 'wb') as f:
                        f.write(await response.read())
                
                print(f"✅ {image_type} 图片已下载: {info['path']}")
                downloaded_paths[image_type] = info["path"]
                
            except Exception as e:
                print(f"❌ 下载 {image_type} 图片失败: {e}")
                downloaded_paths[image_type] = None
    
    return downloaded_paths


async def demo_async_text_to_video():
    """演示异步文生视频"""
    print("\n=== 演示1: 异步文生视频 ===")
    
    generator = AsyncVideoGenerator()
    
    try:
        result = await generator.generate_text_to_video(
            prompt="春天的樱花在微风中飞舞，粉色花瓣缓缓飘落",
            verbose=True
        )
        
        print(f"✅ 异步文生视频成功！")
        print(f"🆔 任务ID: {result.task_id}")
        print(f"📹 视频URL: {result.video_url}")
        
        return result.task_id
        
    except Exception as e:
        print(f"❌ 异步文生视频失败: {e}")
        return None


async def demo_async_image_to_video(image_paths):
    """演示异步图生视频"""
    print("\n=== 演示2: 异步图生视频 ===")
    
    if not image_paths.get("single_frame"):
        print("❌ 无法获取单帧图片，跳过图生视频演示")
        return None
    
    generator = AsyncVideoGenerator()
    
    try:
        result = await generator.generate_image_to_video(
            prompt="女孩睁开眼睛，温柔地微笑，狐狸摇摇尾巴",
            image_url=image_paths["single_frame"],
            verbose=True
        )
        
        print(f"✅ 异步图生视频成功！")
        print(f"🆔 任务ID: {result.task_id}")
        print(f"📹 视频URL: {result.video_url}")
        
        return result.task_id
        
    except Exception as e:
        print(f"❌ 异步图生视频失败: {e}")
        return None


async def demo_first_last_frame_video(image_paths):
    """演示首尾帧视频生成"""
    print("\n=== 演示3: 首尾帧视频生成 ===")
    
    if not image_paths.get("first_frame") or not image_paths.get("last_frame"):
        print("❌ 无法获取首尾帧图片，跳过首尾帧演示")
        return None
    
    generator = AsyncVideoGenerator()
    
    # 使用支持首尾帧的模型配置
    config = VideoGenerationConfig(
        model="wan2-1-14b-flf2v-250417",
        resolution="720p",
        duration=5,
        ratio="adaptive",
        framespersecond=24
    )
    
    try:
        result = await generator.generate_first_last_frame_video(
            prompt="CG动画风格，一只蓝色的小鸟从地面起飞，煽动翅膀，展现飞翔姿态",
            first_frame_url=image_paths["first_frame"],
            last_frame_url=image_paths["last_frame"],
            config=config,
            verbose=True
        )
        
        print(f"✅ 首尾帧视频生成成功！")
        print(f"🆔 任务ID: {result.task_id}")
        print(f"📹 视频URL: {result.video_url}")
        
        return result.task_id
        
    except Exception as e:
        print(f"❌ 首尾帧视频生成失败: {e}")
        return None


async def demo_concurrent_batch_generation(image_paths):
    """演示并发批量生成"""
    print("\n=== 演示4: 并发批量生成 ===")
    
    generator = AsyncVideoGenerator()
    
    # 准备混合类型的生成任务
    prompts = [
        "夏日的海浪轻拍沙滩",
        "秋天的枫叶在风中飘落",
        "冬日的雪花纷纷扬扬"
    ]
    
    # 混合配置：文生视频 + 图生视频 + 首尾帧视频
    configs = [None, None, None]
    image_urls = [None, image_paths.get("single_frame"), None]
    first_frame_urls = [None, None, image_paths.get("first_frame")]
    last_frame_urls = [None, None, image_paths.get("last_frame")]
    
    # 为首尾帧任务设置特殊配置
    if image_paths.get("first_frame") and image_paths.get("last_frame"):
        configs[2] = VideoGenerationConfig(
            model="wan2-1-14b-flf2v-250417",
            resolution="720p",
            duration=5,
            ratio="adaptive"
        )
        prompts[2] = "小鸟从地面起飞到天空中自由飞翔"
    
    try:
        print(f"🚀 开始并发批量生成 {len(prompts)} 个视频...")
        
        results = await generator.generate_videos_batch(
            prompts=prompts,
            configs=configs,
            image_urls=image_urls,
            first_frame_urls=first_frame_urls,
            last_frame_urls=last_frame_urls,
            max_concurrent=2,  # 限制并发数
            poll_interval=10,
            max_wait_time=300,  # 5分钟
            verbose=True
        )
        
        print(f"✅ 并发批量生成完成！成功生成 {len(results)} 个视频")
        
        # 显示结果
        video_types = ["文生视频", "图生视频", "首尾帧视频"]
        for i, result in enumerate(results):
            print(f"  {i+1}. {video_types[i]}: {result.video_url}")
        
        task_ids = [result.task_id for result in results]
        return task_ids
        
    except Exception as e:
        print(f"❌ 并发批量生成失败: {e}")
        return []


async def demo_async_task_management():
    """演示异步任务管理"""
    print("\n=== 演示5: 异步任务管理 ===")
    
    generator = AsyncVideoGenerator()
    
    try:
        # 异步列出不同状态的任务
        print("📋 异步列出任务...")
        
        # 成功的任务
        succeeded_tasks = await generator.list_tasks(status="succeeded", page_size=3)
        print(f"✅ 成功的任务: {succeeded_tasks['total']} 个")
        
        # 正在运行的任务
        running_tasks = await generator.list_tasks(status="running")
        print(f"🔄 正在运行的任务: {running_tasks['total']} 个")
        
        # 排队中的任务
        queued_tasks = await generator.list_tasks(status="queued")
        print(f"⏳ 排队中的任务: {queued_tasks['total']} 个")
        
        print("✅ 异步任务管理演示完成！")
        
    except Exception as e:
        print(f"❌ 异步任务管理失败: {e}")


async def demo_multiple_tasks_waiting():
    """演示多任务并发等待"""
    print("\n=== 演示6: 多任务并发等待 ===")
    
    generator = AsyncVideoGenerator()
    
    try:
        # 创建多个任务但不等待完成
        print("🚀 创建多个任务...")
        task_ids = []
        
        prompts = [
            "宁静的湖面倒映着蓝天白云",
            "可爱的小猫在阳光下打盹"
        ]
        
        for i, prompt in enumerate(prompts):
            result = await generator.create_video_task(prompt)
            task_ids.append(result.task_id)
            print(f"✅ 任务 {i+1} 已创建: {result.task_id}")
        
        # 并发等待所有任务完成
        print("⏳ 并发等待所有任务完成...")
        results = await generator.wait_for_multiple_tasks(
            task_ids,
            poll_interval=10,
            max_wait_time=180,  # 3分钟
            verbose=True
        )
        
        print(f"✅ 多任务等待完成！成功: {len(results)} 个")
        
        return task_ids
        
    except Exception as e:
        print(f"❌ 多任务等待失败: {e}")
        return []


async def cleanup_tasks(task_ids):
    """清理测试任务"""
    if not task_ids:
        return
    
    print(f"\n🧹 清理 {len(task_ids)} 个演示任务...")
    
    generator = AsyncVideoGenerator()
    
    # 并发删除任务
    delete_tasks = [generator.delete_task(task_id) for task_id in task_ids]
    results = await asyncio.gather(*delete_tasks, return_exceptions=True)
    
    success_count = 0
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            print(f"⚠️ 删除任务失败 {task_ids[i]}: {result}")
        else:
            success_count += 1
    
    print(f"✅ 清理完成！成功删除 {success_count} 个任务")


async def cleanup_files():
    """清理演示文件"""
    files_to_remove = [
        "demo_single_frame.png", 
        "demo_first_frame.png", 
        "demo_last_frame.png"
    ]
    
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"🧹 已删除文件: {file_path}")
            except Exception as e:
                print(f"⚠️ 删除文件失败 {file_path}: {e}")


async def main():
    """主演示函数"""
    print("🚀 完整异步视频生成器演示")
    print("=" * 80)
    
    # 检查环境变量
    required_env_vars = ["ARK_API_KEY", "ARK_BASE_URL"]
    missing_vars = [var for var in required_env_vars if not os.environ.get(var)]
    
    if missing_vars:
        print(f"❌ 缺少环境变量: {', '.join(missing_vars)}")
        print("请确保 .env 文件包含所有必需的环境变量")
        return
    
    print("✅ 环境变量检查通过")
    
    # 下载示例图片
    image_paths = await download_sample_images()
    
    # 收集生成的任务ID
    all_task_ids = []
    
    try:
        # 演示异步任务管理
        await demo_async_task_management()
        
        # 询问是否运行实际的视频生成演示
        print(f"\n❓ 是否要运行实际的异步视频生成演示？这将消耗API配额。(y/n): ", end="")
        # 在自动化环境中默认跳过
        user_input = "n"  # input().lower().strip()
        
        if user_input == "y":
            print("🎬 开始异步视频生成演示...")
            
            # 运行各种演示
            task_id = await demo_async_text_to_video()
            if task_id: all_task_ids.append(task_id)
            
            task_id = await demo_async_image_to_video(image_paths)
            if task_id: all_task_ids.append(task_id)
            
            task_id = await demo_first_last_frame_video(image_paths)
            if task_id: all_task_ids.append(task_id)
            
            task_ids = await demo_concurrent_batch_generation(image_paths)
            all_task_ids.extend(task_ids)
            
            task_ids = await demo_multiple_tasks_waiting()
            all_task_ids.extend(task_ids)
        else:
            print("⏭️ 跳过视频生成演示")
        
    finally:
        # 清理任务和文件
        await cleanup_tasks(all_task_ids)
        await cleanup_files()
    
    print("\n" + "=" * 80)
    print("🎉 完整异步视频生成器演示完成！")
    print("💡 功能总结:")
    print("   ✅ 异步文生视频")
    print("   ✅ 异步图生视频（网络URL + 本地文件）")
    print("   ✅ 首尾帧视频生成")
    print("   ✅ 并发批量生成（混合模式）")
    print("   ✅ 多任务并发等待")
    print("   ✅ 异步任务管理")
    print("   ✅ 自动文件处理（base64编码）")
    print("📖 查看 async_video_generator.py 获取完整API文档")


if __name__ == "__main__":
    asyncio.run(main())
