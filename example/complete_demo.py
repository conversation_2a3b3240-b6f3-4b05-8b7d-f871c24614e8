#!/usr/bin/env python3
"""
完整的视频生成器演示脚本

展示VideoGenerator类的所有功能：
- 文生视频
- 图生视频（网络URL）
- 图生视频（本地文件）
- 自定义配置
- 异步工作流
- 任务管理
"""

from video_generator import VideoGenerator, VideoGenerationConfig
import os
import aiohttp
import asyncio
from dotenv import load_dotenv

# 确保加载环境变量
load_dotenv()


async def download_sample_image():
    """下载示例图片用于演示"""
    print("📥 下载示例图片...")
    
    image_url = "https://ark-project.tos-cn-beijing.volces.com/doc_image/i2v_foxrgirl.png"
    local_path = "demo_image.png"
    
    # 如果文件已存在，跳过下载
    if os.path.exists(local_path):
        print(f"✅ 示例图片已存在: {local_path}")
        return local_path
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(image_url) as response:
                response.raise_for_status()
                
                with open(local_path, 'wb') as f:
                    f.write(await response.read())
        
        print(f"✅ 示例图片已下载: {local_path}")
        return local_path
        
    except Exception as e:
        print(f"❌ 下载示例图片失败: {e}")
        return None


def demo_text_to_video():
    """演示文生视频"""
    print("\n=== 演示1: 文生视频 ===")
    
    generator = VideoGenerator()
    
    try:
        result = generator.generate_text_to_video(
            prompt="春天的樱花飞舞，粉色花瓣在微风中飘落，阳光透过花瓣洒在地面上",
            verbose=True
        )
        
        print(f"✅ 文生视频成功！")
        print(f"🆔 任务ID: {result.task_id}")
        print(f"📹 视频URL: {result.video_url}")
        
        return result.task_id
        
    except Exception as e:
        print(f"❌ 文生视频失败: {e}")
        return None


def demo_image_to_video_url():
    """演示图生视频（网络URL）"""
    print("\n=== 演示2: 图生视频（网络URL） ===")
    
    generator = VideoGenerator()
    
    # 使用网络图片URL
    image_url = "https://ark-project.tos-cn-beijing.volces.com/doc_image/i2v_foxrgirl.png"
    
    try:
        result = generator.generate_image_to_video(
            prompt="女孩睁开眼睛，温柔地微笑，狐狸摇摇尾巴，背景中的光线柔和地变化",
            image_url=image_url,
            verbose=True
        )
        
        print(f"✅ 图生视频（网络URL）成功！")
        print(f"🆔 任务ID: {result.task_id}")
        print(f"📹 视频URL: {result.video_url}")
        
        return result.task_id
        
    except Exception as e:
        print(f"❌ 图生视频（网络URL）失败: {e}")
        return None


def demo_image_to_video_local(local_image_path):
    """演示图生视频（本地文件）"""
    print("\n=== 演示3: 图生视频（本地文件） ===")
    
    if not local_image_path or not os.path.exists(local_image_path):
        print("❌ 本地图片不存在，跳过本地文件演示")
        return None
    
    generator = VideoGenerator()
    
    try:
        print(f"📁 使用本地图片: {local_image_path}")
        
        result = generator.generate_image_to_video(
            prompt="女孩眨眨眼，狐狸友善地看向镜头，头发在微风中轻柔摆动",
            image_url=local_image_path,  # 使用本地文件路径
            verbose=True
        )
        
        print(f"✅ 图生视频（本地文件）成功！")
        print(f"🆔 任务ID: {result.task_id}")
        print(f"📹 视频URL: {result.video_url}")
        
        return result.task_id
        
    except Exception as e:
        print(f"❌ 图生视频（本地文件）失败: {e}")
        return None


def demo_custom_config():
    """演示自定义配置"""
    print("\n=== 演示4: 自定义配置 ===")
    
    generator = VideoGenerator()
    
    # 创建自定义配置
    config = VideoGenerationConfig(
        model=os.environ.get("ARK_VIDEO_MODEL"),
        resolution="720p",  # 使用720p分辨率
        duration=3,         # 3秒短视频
        ratio="adaptive",   # 自适应比例
        framespersecond=30, # 30fps
        seed=42             # 固定种子
    )
    
    try:
        print(f"⚙️ 配置: {config.resolution}, {config.duration}秒, {config.framespersecond}fps")
        
        result = generator.generate_text_to_video(
            prompt="可爱的小狗在草地上快乐地奔跑",
            config=config,
            verbose=True
        )
        
        print(f"✅ 自定义配置视频生成成功！")
        print(f"🆔 任务ID: {result.task_id}")
        
        return result.task_id
        
    except Exception as e:
        print(f"❌ 自定义配置视频生成失败: {e}")
        return None


def demo_async_workflow():
    """演示异步工作流"""
    print("\n=== 演示5: 异步工作流 ===")
    
    generator = VideoGenerator()
    
    try:
        # 步骤1: 创建任务
        print("🚀 创建任务...")
        task_result = generator.create_video_task(
            prompt="夕阳西下，海浪轻拍沙滩，海鸥在天空中自由飞翔"
        )
        print(f"✅ 任务已创建: {task_result.task_id}")
        
        # 步骤2: 检查状态
        print("🔍 检查任务状态...")
        status = generator.get_task_status(task_result.task_id)
        print(f"📊 当前状态: {status.status}")
        
        # 步骤3: 等待完成（较短的等待时间）
        print("⏳ 等待任务完成...")
        final_result = generator.wait_for_completion(
            task_result.task_id,
            poll_interval=8,
            max_wait_time=120,  # 2分钟
            verbose=True
        )
        
        print(f"✅ 异步工作流完成！")
        print(f"📹 视频URL: {final_result.video_url}")
        
        return final_result.task_id
        
    except Exception as e:
        print(f"❌ 异步工作流失败: {e}")
        return None


def demo_task_management():
    """演示任务管理"""
    print("\n=== 演示6: 任务管理 ===")
    
    generator = VideoGenerator()
    
    try:
        # 列出最近的成功任务
        print("📋 列出最近的成功任务...")
        tasks = generator.list_tasks(
            status="succeeded",
            page_size=3
        )
        
        print(f"📊 找到 {tasks['total']} 个成功的任务")
        for i, task in enumerate(tasks['items']):
            print(f"  {i+1}. 任务ID: {task['task_id'][:20]}...")
            print(f"     状态: {task['status']}")
        
        # 列出正在运行的任务
        print("\n🔄 检查正在运行的任务...")
        running_tasks = generator.list_tasks(status="running")
        print(f"📊 正在运行的任务数: {running_tasks['total']}")
        
        # 列出排队中的任务
        queued_tasks = generator.list_tasks(status="queued")
        print(f"📊 排队中的任务数: {queued_tasks['total']}")
        
    except Exception as e:
        print(f"❌ 任务管理失败: {e}")


def demo_image_processing():
    """演示图片处理功能"""
    print("\n=== 演示7: 图片处理功能 ===")
    
    generator = VideoGenerator()
    
    # 测试网络URL处理
    network_url = "https://ark-project.tos-cn-beijing.volces.com/doc_image/i2v_foxrgirl.png"
    print(f"🌐 处理网络URL: {network_url}")
    
    try:
        processed_network = generator._process_image_input(network_url)
        print(f"✅ 网络URL处理结果: {processed_network}")
    except Exception as e:
        print(f"❌ 网络URL处理失败: {e}")
    
    # 测试本地文件处理
    local_path = "demo_image.png"
    if os.path.exists(local_path):
        print(f"\n📁 处理本地文件: {local_path}")
        try:
            processed_local = generator._process_image_input(local_path)
            print(f"✅ 本地文件处理成功，生成base64 data URL")
            print(f"📏 Base64长度: {len(processed_local)} 字符")
        except Exception as e:
            print(f"❌ 本地文件处理失败: {e}")
    else:
        print(f"\n⚠️ 本地文件不存在: {local_path}")


async def main():
    """主演示函数"""
    print("🎬 完整视频生成器演示")
    print("=" * 70)
    
    # 检查环境变量
    required_env_vars = ["ARK_API_KEY", "ARK_BASE_URL", "ARK_VIDEO_MODEL"]
    missing_vars = [var for var in required_env_vars if not os.environ.get(var)]
    
    if missing_vars:
        print(f"❌ 缺少环境变量: {', '.join(missing_vars)}")
        print("请确保 .env 文件包含所有必需的环境变量")
        return
    
    print("✅ 环境变量检查通过")
    
    # 下载示例图片
    local_image_path = await download_sample_image()
    
    # 收集生成的任务ID
    task_ids = []
    
    try:
        # 演示图片处理功能
        demo_image_processing()
        
        # 演示任务管理
        demo_task_management()
        
        # 询问是否要运行实际的视频生成演示
        print(f"\n❓ 是否要运行实际的视频生成演示？这将消耗API配额。(y/n): ", end="")
        # 在自动化环境中默认跳过
        user_input = "n"  # input().lower().strip()
        
        if user_input == "y":
            print("🎬 开始视频生成演示...")
            
            # 运行各种演示
            task_id = demo_text_to_video()
            if task_id: task_ids.append(task_id)
            
            task_id = demo_image_to_video_url()
            if task_id: task_ids.append(task_id)
            
            task_id = demo_image_to_video_local(local_image_path)
            if task_id: task_ids.append(task_id)
            
            task_id = demo_custom_config()
            if task_id: task_ids.append(task_id)
            
            task_id = demo_async_workflow()
            if task_id: task_ids.append(task_id)
        else:
            print("⏭️ 跳过视频生成演示")
        
        # 清理任务
        if task_ids:
            print(f"\n🧹 清理演示任务...")
            generator = VideoGenerator()
            for task_id in task_ids:
                try:
                    generator.delete_task(task_id)
                    print(f"✅ 已删除任务: {task_id}")
                except Exception as e:
                    print(f"⚠️ 删除任务失败 {task_id}: {e}")
        
    finally:
        # 清理示例图片
        if local_image_path and os.path.exists(local_image_path):
            try:
                os.remove(local_image_path)
                print(f"🧹 已清理示例图片: {local_image_path}")
            except Exception as e:
                print(f"⚠️ 清理示例图片失败: {e}")
    
    print("\n" + "=" * 70)
    print("🎉 完整演示完成！")
    print("💡 功能总结:")
    print("   ✅ 文生视频")
    print("   ✅ 图生视频（网络URL + 本地文件）")
    print("   ✅ 自定义配置")
    print("   ✅ 异步工作流")
    print("   ✅ 任务管理")
    print("   ✅ 图片处理（自动base64编码）")
    print("📖 查看 README.md 获取详细文档")


if __name__ == "__main__":
    asyncio.run(main())
