#!/usr/bin/env python3
"""
视频生成器演示脚本

展示如何使用新的VideoGenerator处理类
"""

from video_generator import VideoGenerator, VideoGenerationConfig
import os


def demo_simple_generation():
    """演示简单的视频生成"""
    print("=== 演示1: 简单视频生成 ===")
    
    generator = VideoGenerator()
    
    try:
        result = generator.generate_video(
            prompt="一只橘猫在阳光明媚的花园里追蝴蝶",
            verbose=True
        )
        
        print(f"✅ 生成成功！")
        print(f"📹 视频URL: {result.video_url}")
        print(f"🆔 任务ID: {result.task_id}")
        
        return result.task_id
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        return None


def demo_custom_config():
    """演示自定义配置的视频生成"""
    print("\n=== 演示2: 自定义配置视频生成 ===")
    
    generator = VideoGenerator()
    
    # 创建竖屏短视频配置
    config = VideoGenerationConfig(
        model=os.environ.get("ARK_VIDEO_MODEL"),
        resolution="720p",
        duration=3,
        ratio="9:16",  # 竖屏
        framespersecond=30,
        seed=42  # 固定种子确保可复现
    )
    
    try:
        print(f"📝 配置: {config.resolution}, {config.ratio}, {config.duration}秒, {config.framespersecond}fps")
        
        result = generator.generate_video(
            prompt="可爱的柴犬在雪地里打滚",
            config=config,
            verbose=True
        )
        
        print(f"✅ 自定义配置生成成功！")
        print(f"📹 视频URL: {result.video_url}")
        
        return result.task_id
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        return None


def demo_async_workflow():
    """演示异步工作流"""
    print("\n=== 演示3: 异步工作流 ===")
    
    generator = VideoGenerator()
    
    try:
        # 步骤1: 创建任务
        print("🚀 创建任务...")
        task_result = generator.create_video_task(
            prompt="夕阳西下，海浪轻拍沙滩，海鸥在天空飞翔"
        )
        print(f"✅ 任务已创建: {task_result.task_id}")
        
        # 步骤2: 检查状态
        print("🔍 检查任务状态...")
        status = generator.get_task_status(task_result.task_id)
        print(f"📊 当前状态: {status.status}")
        
        # 步骤3: 等待完成
        print("⏳ 等待任务完成...")
        final_result = generator.wait_for_completion(
            task_result.task_id,
            poll_interval=8,
            verbose=True
        )
        
        print(f"✅ 异步任务完成！")
        print(f"📹 视频URL: {final_result.video_url}")
        
        return final_result.task_id
        
    except Exception as e:
        print(f"❌ 异步工作流失败: {e}")
        return None


def demo_task_management():
    """演示任务管理功能"""
    print("\n=== 演示4: 任务管理 ===")
    
    generator = VideoGenerator()
    
    try:
        # 列出最近的成功任务
        print("📋 列出最近的成功任务...")
        tasks = generator.list_tasks(
            status="succeeded",
            page_size=5
        )
        
        print(f"📊 找到 {tasks['total']} 个成功的任务")
        for i, task in enumerate(tasks['items'][:3]):  # 只显示前3个
            print(f"  {i+1}. 任务ID: {task['task_id'][:20]}...")
            print(f"     状态: {task['status']}")
            print(f"     创建时间: {task['created_at']}")
        
        # 列出正在运行的任务
        print("\n🔄 检查正在运行的任务...")
        running_tasks = generator.list_tasks(status="running")
        print(f"📊 正在运行的任务数: {running_tasks['total']}")
        
    except Exception as e:
        print(f"❌ 任务管理失败: {e}")


def demo_prompt_with_params():
    """演示提示词参数构建"""
    print("\n=== 演示5: 提示词参数构建 ===")
    
    generator = VideoGenerator()
    
    # 创建配置
    config = VideoGenerationConfig(
        model=os.environ.get("ARK_VIDEO_MODEL"),
        resolution="1080p",
        duration=6,
        ratio="16:9",
        framespersecond=24,
        seed=123
    )
    
    # 展示参数如何添加到提示词中
    base_prompt = "春天的樱花飞舞，粉色花瓣在微风中飘落"
    full_prompt = generator._build_prompt_with_params(base_prompt, config)
    
    print(f"📝 基础提示词: {base_prompt}")
    print(f"🔧 完整提示词: {full_prompt}")
    print(f"💡 参数说明:")
    print(f"   --rs {config.resolution}  # 分辨率")
    print(f"   --rt {config.ratio}       # 宽高比")
    print(f"   --dur {config.duration}   # 时长(秒)")
    print(f"   --fps {config.framespersecond}  # 帧率")
    print(f"   --seed {config.seed}      # 随机种子")


def main():
    """主演示函数"""
    print("🎬 视频生成器处理类演示")
    print("=" * 60)
    
    # 检查环境变量
    if not all([os.environ.get("ARK_API_KEY"), 
                os.environ.get("ARK_BASE_URL"), 
                os.environ.get("ARK_VIDEO_MODEL")]):
        print("❌ 请确保设置了所有必需的环境变量")
        return
    
    print("✅ 环境变量检查通过")
    
    # 收集生成的任务ID
    task_ids = []
    
    # 运行演示
    task_id = demo_simple_generation()
    if task_id:
        task_ids.append(task_id)
    
    task_id = demo_custom_config()
    if task_id:
        task_ids.append(task_id)
    
    task_id = demo_async_workflow()
    if task_id:
        task_ids.append(task_id)
    
    demo_task_management()
    demo_prompt_with_params()
    
    # 清理演示任务
    if task_ids:
        print(f"\n🧹 清理演示任务...")
        generator = VideoGenerator()
        for task_id in task_ids:
            try:
                generator.delete_task(task_id)
                print(f"✅ 已删除任务: {task_id}")
            except Exception as e:
                print(f"⚠️ 删除任务失败 {task_id}: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 演示完成！")
    print("💡 提示: 查看 example_usage.py 获取更多使用示例")
    print("📖 提示: 查看 README.md 获取完整文档")


if __name__ == "__main__":
    main()
