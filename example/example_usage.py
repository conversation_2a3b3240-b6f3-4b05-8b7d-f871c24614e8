#!/usr/bin/env python3
"""
视频生成器使用示例

这个文件展示了如何使用VideoGenerator类的各种功能
"""

from video_generator import VideoGenerator, VideoGenerationConfig
import os


def example_basic_usage():
    """基础使用示例"""
    print("=== 基础使用示例 ===")
    
    # 创建视频生成器
    generator = VideoGenerator()
    
    # 简单生成视频（使用默认配置）
    try:
        result = generator.generate_video(
            prompt="一只可爱的小猫在花园里玩耍",
            verbose=True
        )
        print(f"生成成功！视频URL: {result.video_url}")
    except Exception as e:
        print(f"生成失败: {e}")


def example_custom_config():
    """自定义配置示例"""
    print("\n=== 自定义配置示例 ===")
    
    generator = VideoGenerator()
    
    # 创建自定义配置
    config = VideoGenerationConfig(
        model=os.environ.get("ARK_VIDEO_MODEL"),
        resolution="720p",  # 使用720p分辨率
        duration=8,         # 8秒视频
        ratio="9:16",       # 竖屏比例
        framespersecond=30, # 30fps
        seed=12345          # 固定种子以便复现
    )
    
    try:
        result = generator.generate_video(
            prompt="科技感十足的未来城市夜景，霓虹灯闪烁",
            config=config,
            verbose=True
        )
        print(f"自定义配置生成成功！")
    except Exception as e:
        print(f"生成失败: {e}")


def example_async_workflow():
    """异步工作流示例"""
    print("\n=== 异步工作流示例 ===")
    
    generator = VideoGenerator()
    
    try:
        # 1. 创建任务但不等待完成
        result = generator.create_video_task(
            prompt="宁静的海边日落，海浪轻拍沙滩"
        )
        print(f"任务已创建: {result.task_id}")
        
        # 2. 手动检查状态
        status = generator.get_task_status(result.task_id)
        print(f"当前状态: {status.status}")
        
        # 3. 等待完成
        final_result = generator.wait_for_completion(
            result.task_id,
            poll_interval=5,  # 5秒轮询一次
            verbose=True
        )
        
        print(f"任务完成！视频URL: {final_result.video_url}")
        
    except Exception as e:
        print(f"异步工作流失败: {e}")


def example_task_management():
    """任务管理示例"""
    print("\n=== 任务管理示例 ===")
    
    generator = VideoGenerator()
    
    try:
        # 列出所有成功的任务
        print("列出成功的任务:")
        tasks = generator.list_tasks(status="succeeded", page_size=5)
        print(f"总共有 {tasks['total']} 个成功的任务")
        
        for task in tasks['items']:
            print(f"- 任务ID: {task['task_id']}, 状态: {task['status']}")
        
        # 列出正在运行的任务
        print("\n列出正在运行的任务:")
        running_tasks = generator.list_tasks(status="running")
        print(f"正在运行的任务数: {running_tasks['total']}")
        
    except Exception as e:
        print(f"任务管理失败: {e}")


def example_batch_generation():
    """批量生成示例"""
    print("\n=== 批量生成示例 ===")
    
    generator = VideoGenerator()
    
    prompts = [
        "春天的樱花飞舞",
        "夏日的海滩度假",
        "秋天的枫叶飘落",
        "冬日的雪花纷飞"
    ]
    
    task_ids = []
    
    try:
        # 批量创建任务
        print("批量创建任务...")
        for i, prompt in enumerate(prompts):
            result = generator.create_video_task(prompt)
            task_ids.append(result.task_id)
            print(f"任务 {i+1} 已创建: {result.task_id}")
        
        # 等待所有任务完成
        print("\n等待所有任务完成...")
        completed_results = []
        
        for i, task_id in enumerate(task_ids):
            print(f"等待任务 {i+1} 完成...")
            try:
                result = generator.wait_for_completion(
                    task_id, 
                    verbose=False,
                    max_wait_time=300  # 5分钟超时
                )
                completed_results.append(result)
                print(f"任务 {i+1} 完成！")
            except Exception as e:
                print(f"任务 {i+1} 失败: {e}")
        
        # 显示结果
        print(f"\n批量生成完成！成功生成 {len(completed_results)} 个视频")
        for i, result in enumerate(completed_results):
            print(f"视频 {i+1}: {result.video_url}")
            
    except Exception as e:
        print(f"批量生成失败: {e}")


def example_error_handling():
    """错误处理示例"""
    print("\n=== 错误处理示例 ===")
    
    generator = VideoGenerator()
    
    # 示例1: 处理无效的任务ID
    try:
        generator.get_task_status("invalid-task-id")
    except Exception as e:
        print(f"预期的错误 - 无效任务ID: {e}")
    
    # 示例2: 处理空的提示词
    try:
        generator.create_video_task("")
    except Exception as e:
        print(f"预期的错误 - 空提示词: {e}")
    
    # 示例3: 处理删除不存在的任务
    try:
        generator.delete_task("non-existent-task")
    except Exception as e:
        print(f"预期的错误 - 删除不存在的任务: {e}")


if __name__ == "__main__":
    print("视频生成器使用示例")
    print("=" * 50)
    
    # 运行所有示例
    example_basic_usage()
    example_custom_config()
    example_async_workflow()
    example_task_management()
    example_batch_generation()
    example_error_handling()
    
    print("\n所有示例运行完成！")
