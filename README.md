# 视频生成器 (Video Generator)

基于火山引擎视频生成API的Python处理类，提供简洁易用的接口来生成AI视频。支持文生视频、图生视频和首尾帧视频生成三种模式。

## 📋 目录

- [🌟 功能特性](#-功能特性)
- [📦 安装依赖](#-安装依赖)
- [⚙️ 环境配置](#️-环境配置)
- [🚀 快速开始](#-快速开始)
  - [1. 文生视频](#1-文生视频基础使用)
  - [2. 图生视频](#2-图生视频单帧)
  - [3. 首尾帧视频生成](#3-首尾帧视频生成--新功能)
  - [4. 异步批量生成](#4-异步批量生成)
- [🖼️ 图片输入支持](#️-图片输入支持)
- [📚 API 参考](#-api-参考)
  - [VideoGenerator 类](#videogenerator-类同步版本)
  - [AsyncVideoGenerator 类](#asyncvideogenerator-类异步版本)
  - [VideoGenerationConfig 类](#videogenerationconfig-类)
- [📁 项目结构和示例](#-项目结构和示例)
- [⚠️ 注意事项](#️-注意事项)
- [🔧 故障排除](#-故障排除)
- [📞 技术支持](#-技术支持)

## 🌟 功能特性

### 核心功能
- 🎬 **文生视频**: 基于文本提示词生成视频
- 🖼️ **图生视频**: 基于首帧图片和文本提示词生成视频
- 🎭 **首尾帧视频**: 基于首帧和尾帧图片生成过渡视频 ⭐ **新功能**
- ⚙️ **灵活配置**: 支持分辨率、时长、比例、帧率等参数自定义
- 🔄 **智能轮询**: 自动等待任务完成，支持自定义轮询间隔

### 高级功能
- 📋 **任务管理**: 创建、查询、列出、删除任务
- 🚀 **异步支持**: AsyncVideoGenerator类支持并发处理
- 🔀 **批量处理**: 支持批量创建和管理多个视频生成任务
- 🖥️ **本地图片支持**: 自动处理本地图片文件，转换为base64编码
- 🌐 **网络图片支持**: 直接使用网络URL
- 🛡️ **错误处理**: 完善的异常处理和错误信息
- 🔄 **向后兼容**: 保持所有原有接口不变

## 安装依赖

```bash
pip install volcengine-python-sdk[ark] python-dotenv
```

## ⚙️ 环境配置

创建 `.env` 文件并配置以下环境变量：

### 基础API配置（必需）
```env
ARK_API_KEY=your_api_key_here
ARK_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
```

### 视频生成配置 ⭐ **新功能**
```env
# 模型配置（必需）
ARK_VIDEO_MODEL=doubao-seedance-1-0-pro-250528

# 视频参数配置（可选）
ARK_VIDEO_RESOLUTION=1080p
ARK_VIDEO_DURATION=5
ARK_VIDEO_RATIO=adaptive
ARK_VIDEO_FRAMESPERSECOND=24
ARK_VIDEO_SEED=12345
```

### 配置说明
- **ARK_VIDEO_MODEL**: 视频生成模型ID（**必需**）
- **ARK_VIDEO_RESOLUTION**: 分辨率 (720p, 1080p)
- **ARK_VIDEO_DURATION**: 视频时长（秒）
- **ARK_VIDEO_RATIO**: 宽高比 (adaptive, 16:9, 9:16, 1:1)
- **ARK_VIDEO_FRAMESPERSECOND**: 帧率
- **ARK_VIDEO_SEED**: 随机种子（可选，用于复现结果）

**重要**：`ARK_VIDEO_MODEL` 是必需的环境变量，如果未设置将会报错。其他视频配置参数是可选的，如果不设置会使用代码中的默认值。

### 自动配置加载 ⭐ **新功能**

VideoGenerator 现在会自动从环境变量加载配置，无需手动调用配置方法：

```python
# 只需要在 .env 文件中设置配置
# ARK_VIDEO_MODEL=doubao-seedance-1-0-pro-250528
# ARK_VIDEO_RESOLUTION=720p
# ARK_VIDEO_DURATION=8

# Python 代码中直接使用，配置自动生效
generator = VideoGenerator()  # 自动加载环境变量配置
result = generator.generate_text_to_video("提示词")
```

**配置优先级**：系统环境变量 > .env文件 > 默认值（Docker友好）

## 🚀 快速开始

### 1. 文生视频（基础使用）

```python
from video_generator import VideoGenerator

# 创建视频生成器（自动从环境变量加载配置）⭐ **新功能**
generator = VideoGenerator()

# 生成视频（使用环境变量配置）
result = generator.generate_text_to_video("一只可爱的小猫在花园里玩耍")
print(f"视频URL: {result.video_url}")
```

**说明**：VideoGenerator 现在会自动从环境变量加载配置，无需手动调用 `VideoGenerationConfig.from_env()`

### 2. 图生视频

```python
from video_generator import VideoGenerator

generator = VideoGenerator()

# 使用网络图片URL生成视频
result = generator.generate_image_to_video(
    prompt="女孩睁开眼，温柔地看向镜头，头发被风吹动",
    image_url="https://example.com/your-image.jpg"
)
print(f"视频URL: {result.video_url}")

# 使用本地图片文件生成视频
result = generator.generate_image_to_video(
    prompt="女孩睁开眼，温柔地看向镜头，头发被风吹动",
    image_url="./local_image.jpg"  # 本地文件路径
)
print(f"视频URL: {result.video_url}")
```

### 3. 首尾帧视频生成 ⭐ **新功能**

#### 同步版本
```python
from video_generator import VideoGenerator, VideoGenerationConfig

generator = VideoGenerator()

# 创建首尾帧视频配置
config = VideoGenerationConfig(
    model="wan2-1-14b-flf2v-250417",  # 使用支持首尾帧的模型
    resolution="720p",
    duration=5,
    ratio="adaptive"
)

# 基于首尾帧生成视频
result = generator.generate_first_last_frame_video(
    prompt="CG动画风格，一只蓝色的小鸟从地面起飞，煽动翅膀",
    first_frame_url="./first_frame.png",
    last_frame_url="./last_frame.png",
    config=config
)
print(f"视频URL: {result.video_url}")
```

#### 异步版本
```python
import asyncio
from async_video_generator import AsyncVideoGenerator

async def main():
    generator = AsyncVideoGenerator()

    # 基于首尾帧生成视频
    result = await generator.generate_first_last_frame_video(
        prompt="CG动画风格，一只蓝色的小鸟从地面起飞，煽动翅膀",
        first_frame_url="./first_frame.png",
        last_frame_url="./last_frame.png"
    )
    print(f"视频URL: {result.video_url}")

asyncio.run(main())
```

### 4. 异步批量生成

```python
import asyncio
from async_video_generator import AsyncVideoGenerator

async def main():
    generator = AsyncVideoGenerator()

    # 混合模式批量生成
    results = await generator.generate_videos_batch(
        prompts=[
            "春天的樱花飞舞",
            "女孩微笑",
            "小鸟飞翔"
        ],
        image_urls=[None, "./girl.jpg", None],
        first_frame_urls=[None, None, "./bird_start.jpg"],
        last_frame_urls=[None, None, "./bird_end.jpg"],
        max_concurrent=2
    )

    print(f"成功生成 {len(results)} 个视频！")

asyncio.run(main())
```

### 自定义配置

```python
from video_generator import VideoGenerator, VideoGenerationConfig

generator = VideoGenerator()

# 创建自定义配置
config = VideoGenerationConfig(
    model="doubao-seedance-1-0-pro-250528",
    resolution="1080p",  # 720p, 1080p
    duration=8,          # 视频时长（秒）
    ratio="16:9",        # 16:9, 9:16, 1:1
    framespersecond=30,  # 帧率
    seed=12345           # 随机种子（可选）
)

result = generator.generate_video(
    prompt="科技感十足的未来城市夜景",
    config=config
)
```

**注意**: 视频参数会自动添加到提示词中，例如上面的配置会生成如下提示词：
```text
科技感十足的未来城市夜景 --rs 1080p --rt adaptive --dur 8 --fps 30 --seed 12345
```

## 🖼️ 图片输入支持

### 支持的输入方式

- **网络URL**: 直接使用 `http://` 或 `https://` 开头的图片链接
- **本地文件**: 支持本地图片文件路径，会自动转换为base64编码
- **相对路径**: 支持相对于当前工作目录的路径
- **绝对路径**: 支持完整的文件系统路径

### 支持的图片格式

- PNG (.png)
- JPEG (.jpg, .jpeg)
- GIF (.gif)
- BMP (.bmp)
- WebP (.webp)

### 三种视频生成模式

#### 1. 文生视频
```python
# 纯文本生成视频
result = generator.generate_text_to_video("春天的樱花飞舞")
```

#### 2. 图生视频（单帧）
```python
# 网络图片
result = generator.generate_image_to_video(
    prompt="女孩微笑，头发飞舞",
    image_url="https://example.com/image.jpg"
)

# 本地图片
result = generator.generate_image_to_video(
    prompt="女孩微笑，头发飞舞",
    image_url="./local_image.png"
)
```

#### 3. 首尾帧视频（双帧）⭐ **新功能**
```python
# 基于首尾帧生成过渡视频
result = generator.generate_first_last_frame_video(
    prompt="小鸟从地面起飞到天空中飞翔",
    first_frame_url="./bird_ground.png",    # 首帧：地面上的小鸟
    last_frame_url="./bird_sky.png"         # 尾帧：天空中的小鸟
)
```

### 自动图片处理

系统会自动处理不同类型的图片输入：

```python
# 网络URL - 直接使用
image_url = "https://example.com/image.jpg"

# 本地文件 - 自动转换为base64
image_url = "./local_image.png"
# 内部处理：读取文件 → base64编码 → data:image/png;base64,xxx

# 混合使用
result = generator.generate_first_last_frame_video(
    prompt="变化过程",
    first_frame_url="https://example.com/start.jpg",  # 网络图片
    last_frame_url="./end.png"                        # 本地图片
)
```

### 异步工作流

```python
# 创建任务但不等待完成
task_result = generator.create_video_task("宁静的海边日落")
print(f"任务ID: {task_result.task_id}")

# 手动检查状态
status = generator.get_task_status(task_result.task_id)
print(f"当前状态: {status.status}")

# 等待完成
final_result = generator.wait_for_completion(
    task_result.task_id,
    poll_interval=5,  # 5秒轮询一次
    max_wait_time=600 # 最大等待10分钟
)
```

## 📚 API 参考

### VideoGenerator 类（同步版本）

#### 初始化
```python
generator = VideoGenerator(base_url=None, api_key=None)
```

#### 核心方法

##### generate_video()
一站式视频生成方法（支持所有模式）
```python
# 文生视频
result = generator.generate_video(
    prompt="视频描述",
    config=VideoGenerationConfig(...),
    wait_for_completion=True,
    poll_interval=10,
    max_wait_time=600,
    verbose=True
)

# 图生视频
result = generator.generate_video(
    prompt="动作描述",
    image_url="https://example.com/image.jpg",
    config=VideoGenerationConfig(...),
    wait_for_completion=True
)

# 首尾帧视频
result = generator.generate_video(
    prompt="变化过程描述",
    first_frame_url="./start.jpg",
    last_frame_url="./end.jpg",
    config=VideoGenerationConfig(...),
    wait_for_completion=True
)
```

##### generate_text_to_video()
文生视频专用方法
```python
result = generator.generate_text_to_video(
    prompt="视频描述",
    config=VideoGenerationConfig(...),
    wait_for_completion=True,
    poll_interval=10,
    max_wait_time=600,
    verbose=True
)
```

##### generate_image_to_video()
图生视频专用方法
```python
result = generator.generate_image_to_video(
    prompt="动作描述",
    image_url="https://example.com/image.jpg",
    config=VideoGenerationConfig(...),
    wait_for_completion=True,
    poll_interval=10,
    max_wait_time=600,
    verbose=True
)
```

##### generate_first_last_frame_video() ⭐ **新方法**
首尾帧视频专用方法
```python
result = generator.generate_first_last_frame_video(
    prompt="变化过程描述",
    first_frame_url="./start.jpg",
    last_frame_url="./end.jpg",
    config=VideoGenerationConfig(...),
    wait_for_completion=True,
    poll_interval=10,
    max_wait_time=600,
    verbose=True
)
```

##### create_video_task()
创建视频生成任务
```python
result = generator.create_video_task(prompt, config)
```

##### get_task_status()
获取任务状态
```python
status = generator.get_task_status(task_id)
```

##### wait_for_completion()
等待任务完成
```python
result = generator.wait_for_completion(task_id, poll_interval=10)
```

##### list_tasks()
列出任务
```python
tasks = generator.list_tasks(
    page_num=1,
    page_size=10,
    status="succeeded",  # queued, running, succeeded, failed, cancelled
    model=None,
    task_ids=None
)
```

##### delete_task()
删除任务
```python
success = generator.delete_task(task_id)
```

### AsyncVideoGenerator 类（异步版本）

异步版本的视频生成器，支持并发处理、批量生成和首尾帧视频生成。

#### 初始化
```python
generator = AsyncVideoGenerator(base_url=None, api_key=None)
```

#### 异步核心方法

##### 所有同步方法的异步版本
```python
# 异步文生视频
result = await generator.generate_text_to_video(prompt="视频描述")

# 异步图生视频
result = await generator.generate_image_to_video(
    prompt="动作描述",
    image_url="./image.jpg"
)

# 异步首尾帧视频
result = await generator.generate_first_last_frame_video(
    prompt="变化过程",
    first_frame_url="./start.jpg",
    last_frame_url="./end.jpg"
)
```

#### 高级异步功能

##### generate_videos_batch()
批量异步生成视频（支持混合模式）
```python
results = await generator.generate_videos_batch(
    prompts=[
        "春天的樱花",           # 文生视频
        "女孩微笑",             # 图生视频
        "小鸟飞翔"              # 首尾帧视频
    ],
    image_urls=[None, "./girl.jpg", None],
    first_frame_urls=[None, None, "./bird_start.jpg"],
    last_frame_urls=[None, None, "./bird_end.jpg"],
    max_concurrent=3,
    poll_interval=10,
    max_wait_time=600,
    verbose=True
)
```

##### wait_for_multiple_tasks()
并发等待多个任务完成
```python
# 先创建多个任务
task1 = await generator.create_video_task("提示词1")
task2 = await generator.create_video_task("提示词2")

# 并发等待完成
results = await generator.wait_for_multiple_tasks(
    task_ids=[task1.task_id, task2.task_id],
    poll_interval=10,
    max_wait_time=600,
    verbose=True
)
```

#### 异步任务管理

```python
# 异步创建任务
result = await generator.create_video_task(prompt, config, image_url, first_frame_url, last_frame_url)

# 异步获取状态
status = await generator.get_task_status(task_id)

# 异步等待完成
result = await generator.wait_for_completion(task_id, poll_interval=10)

# 异步列出任务
tasks = await generator.list_tasks(status="succeeded", page_size=10)

# 异步删除任务
success = await generator.delete_task(task_id)
```

### VideoGenerationConfig 类

视频生成配置参数：

#### 手动创建配置
```python
config = VideoGenerationConfig(
    model="doubao-seedance-1-0-pro-250528",  # 必需，模型ID
    resolution="1080p",     # 分辨率：720p, 1080p
    duration=5,             # 视频时长（秒）
    ratio="adaptive",       # 宽高比：adaptive, 16:9, 9:16, 1:1
    framespersecond=24,     # 帧率：24, 30等
    seed=None               # 随机种子（可选，用于复现结果）
)
```

#### 从环境变量创建配置 ⭐ **新功能**
```python
# 使用环境变量的默认配置
config = VideoGenerationConfig.from_env()

# 使用环境变量但覆盖某些参数
config = VideoGenerationConfig.from_env(
    duration=10,        # 覆盖环境变量中的时长
    resolution="720p"   # 覆盖环境变量中的分辨率
)
```

#### 自动配置选择
```python
# VideoGenerator会根据输入自动选择合适的配置
generator = VideoGenerator()

# 文生视频 - 自动使用文生视频配置
result = generator.generate_text_to_video("提示词")

# 首尾帧视频 - 自动使用首尾帧配置
result = generator.generate_first_last_frame_video(
    prompt="提示词",
    first_frame_url="start.jpg",
    last_frame_url="end.jpg"
    # config参数为None时会自动选择首尾帧模式的配置
)
```

#### 模型支持说明

| 功能 | 推荐模型 | 说明 |
|------|----------|------|
| 文生视频 | `doubao-seedance-1-0-pro-250528` | 通用文生视频模型 |
| 图生视频 | `doubao-seedance-1-0-pro-250528` | 支持单帧图片输入 |
| 首尾帧视频 | `wan2-1-14b-flf2v-250417` | 专门支持首尾帧的模型 |

**重要**：模型选择完全由 `ARK_VIDEO_MODEL` 环境变量控制。系统不会自动选择模型，请根据使用场景在环境变量中设置合适的模型。

#### 参数详解

- **model**: 模型ID，不同模型支持不同功能
- **resolution**: 视频分辨率，影响视频质量和生成时间
- **duration**: 视频时长，单位为秒，建议3-10秒
- **ratio**: 宽高比，`adaptive`会根据输入图片自动调整
- **framespersecond**: 帧率，影响视频流畅度
- **seed**: 随机种子，相同种子可以复现相同结果

### TaskResult 类

任务结果对象：

```python
result = TaskResult(
    task_id="cgt-xxx",
    status="succeeded",
    model="doubao-seedance-1-0-pro-250528",
    video_url="https://...",
    error=None,
    usage={"completion_tokens": 246840},
    created_at=1753421822,
    updated_at=1753421867,
    seed=30970,
    revised_prompt=None
)
```

## 📁 项目结构和示例

### 核心文件

- `video_generator.py` - 同步视频生成器主类
- `async_video_generator.py` - 异步视频生成器主类

### 测试文件

- `test_video_generator.py` - 基本功能测试
- `test_async_video_generator.py` - 异步功能测试
- `test_image_to_video.py` - 图生视频功能测试
- `test_first_last_frame.py` - 首尾帧功能测试
- `test_sync_first_last_frame.py` - 同步首尾帧功能测试

### 示例文件

- `example/example_usage.py` - 基础使用示例
- `example/complete_demo.py` - 完整功能演示
- `example/complete_async_demo.py` - 异步功能演示

### 运行示例

```bash
# 基础功能测试
uv run python test_video_generator.py

# 异步功能演示
uv run python example/complete_async_demo.py

# 首尾帧功能测试
uv run python test_first_last_frame.py
```

## 错误处理

所有方法都会抛出有意义的异常信息：

```python
try:
    result = generator.generate_video("视频描述")
except Exception as e:
    print(f"生成失败: {e}")
```

## ⚠️ 注意事项

### API使用限制
1. **调用频率**: 请注意火山引擎API的调用频率限制
2. **并发数量**: 建议异步批量生成时控制并发数（推荐2-3个）
3. **配额管理**: 视频生成会消耗API配额，请合理使用

### 模型和参数
1. **模型配置**:
   - **必须**在 `.env` 文件或环境变量中设置 `ARK_VIDEO_MODEL`
   - 文生视频和图生视频：推荐 `doubao-seedance-1-0-pro-250528`
   - 首尾帧视频：推荐 `wan2-1-14b-flf2v-250417`
   - 系统不会自动选择模型，未设置将报错
2. **视频时长**: 建议3-10秒，过长可能影响生成质量
3. **分辨率**: 720p生成速度更快，1080p质量更高
4. **宽高比**: 首尾帧模型建议使用 `adaptive`

### 图片处理
1. **文件大小**: 建议图片文件小于10MB
2. **图片质量**: 高质量图片能获得更好的视频效果
3. **格式支持**: 推荐使用PNG或JPEG格式
4. **本地路径**: 确保图片文件路径正确且可访问

### 网络和超时
1. **网络稳定**: 视频生成需要稳定的网络连接
2. **超时设置**: 建议设置10-15分钟的超时时间
3. **重试机制**: 网络异常时可以重试任务查询

### 结果处理
1. **URL有效期**: 生成的视频URL有时效性，请及时下载
2. **存储建议**: 重要视频请下载到本地存储
3. **任务清理**: 及时删除不需要的任务以节省资源

## 🔧 故障排除

### 常见问题

**Q: 首尾帧视频生成失败？**
A: 确保使用正确的模型 `wan2-1-14b-flf2v-250417` 和 `ratio="adaptive"`

**Q: 本地图片无法识别？**
A: 检查文件路径是否正确，文件是否存在，格式是否支持

**Q: 异步批量生成部分失败？**
A: 降低并发数量，检查网络连接，增加超时时间

**Q: 视频生成时间过长？**
A: 尝试降低分辨率，缩短视频时长，或检查API服务状态

## 📞 技术支持

- 查看示例代码：`example/` 目录
- 运行测试：`test_*.py` 文件
- 火山引擎文档：https://www.volcengine.com/docs/82379/1520757

## 📄 许可证

MIT License
