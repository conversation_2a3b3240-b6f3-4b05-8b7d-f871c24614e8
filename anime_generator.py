import os
import base64
import mimetypes
import asyncio
import aiohttp
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv
from volcenginesdkarkruntime import Ark

load_dotenv()

class AnimeStyleGenerator:
    """动漫风格图片生成器"""

    def __init__(self, download_images=False, download_dir="generated_images"):
        """
        初始化动漫风格生成器

        Args:
            download_images (bool): 是否自动下载生成的图片到本地，默认为False
            download_dir (str): 图片下载目录，默认为"generated_images"
        """
        self.client = Ark(
            base_url=os.environ.get("ARK_BASE_URL"),
            api_key=os.environ.get("ARK_API_KEY"),
        )

        # 图片下载配置
        self.download_images = download_images
        self.download_dir = Path(download_dir)

        # 如果启用下载功能，确保下载目录存在
        if self.download_images:
            self._ensure_download_directory()
        
        # 定义各种风格的提示词模板
        self.styles = {
            "traditional_anime": {
                "name": "传统日式动漫风格",
                "prompt": "传统日式动漫风格，大眼睛，鲜艳发色，动态姿势，背景细腻，强调情感表达，类似海贼王或火影忍者的画风",
                "description": "大眼睛、鲜艳发色、动态姿势，背景细腻，强调情感表达"
            },
            "manga_sketch": {
                "name": "手绘漫画风格",
                "prompt": "黑白手绘漫画风格，黑白线条为主，线条粗细变化，动态感强，类似死亡笔记或钢之炼金术师的画风",
                "description": "黑白线条为主，线条粗细变化，动态感强"
            },
            "cel_shading": {
                "name": "赛璐璐风格",
                "prompt": "赛璐璐动画风格，平涂色彩，清晰黑色描边，画面明快干净，类似龙珠或美少女战士的画风",
                "description": "平涂色彩，清晰黑色描边，画面明快干净"
            },
            "ghibli": {
                "name": "吉卜力风格",
                "prompt": "吉卜力工作室风格，宫崎骏画风，柔和色调，手绘质感，充满自然与魔幻元素，类似千与千寻的画风",
                "description": "柔和色调，手绘质感，充满自然与魔幻元素"
            },
            "cyberpunk": {
                "name": "赛博朋克动漫风格",
                "prompt": "赛博朋克动漫风格，未来科技感，霓虹灯光效果，冷色调，带机械元素，类似攻壳机动队的画风",
                "description": "未来科技、霓虹灯光、冷色调，带机械元素"
            },
            "chibi": {
                "name": "Q版可爱风",
                "prompt": "Q版可爱风格，大头小身，圆润眼睛，色彩明亮，表情夸张可爱，萌系画风",
                "description": "大头小身，圆润眼睛，色彩明亮，表情夸张可爱"
            },
            "steampunk": {
                "name": "蒸汽朋克动漫风格",
                "prompt": "蒸汽朋克动漫风格，维多利亚时代与机械融合，棕色金色调，复古科技感，类似蒸汽男孩的画风",
                "description": "维多利亚时代与机械融合，棕色、金色调，复古科技感"
            },
            "dark_fantasy": {
                "name": "暗黑奇幻风格",
                "prompt": "暗黑奇幻动漫风格，阴郁色调，哥特或魔幻元素，氛围神秘恐怖，类似东京喰种的画风",
                "description": "阴郁色调，哥特或魔幻元素，氛围神秘恐怖"
            },
            "retro_80s": {
                "name": "复古80-90年代风格",
                "prompt": "复古80-90年代日漫风格，颗粒感，低饱和度色彩，带有老式CRT电视效果，类似星际牛仔的画风",
                "description": "颗粒感、低饱和度色彩，带有老式CRT电视效果"
            },
            "watercolor": {
                "name": "水彩风动漫",
                "prompt": "水彩风动漫风格，柔和边缘，渐变色彩，艺术感强，画面透明感十足，唯美梦幻",
                "description": "柔和边缘，渐变色彩，艺术感强，画面透明感十足"
            }
        }

    def _ensure_download_directory(self):
        """确保下载目录存在"""
        try:
            self.download_dir.mkdir(parents=True, exist_ok=True)
            print(f"📁 下载目录已准备: {self.download_dir.absolute()}")
        except Exception as e:
            print(f"❌ 创建下载目录失败: {e}")
            raise

    def _generate_filename(self, style_key, seed, extension="jpg"):
        """
        生成唯一的文件名

        Args:
            style_key (str): 风格键名
            seed (int): 随机种子
            extension (str): 文件扩展名，默认为"jpg"

        Returns:
            str: 生成的文件名
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{style_key}_{seed}_{timestamp}.{extension}"

    async def download_image(self, image_url, filename=None, timeout=30):
        """
        异步下载图片到本地

        Args:
            image_url (str): 图片URL
            filename (str, optional): 自定义文件名，如果不提供则自动生成
            timeout (int): 下载超时时间（秒），默认30秒

        Returns:
            str: 下载的文件路径，如果下载失败返回None
        """
        if not self.download_images:
            print("⚠️  图片下载功能未启用")
            return None

        try:
            print(f"📥 开始下载图片...")

            # 创建超时配置
            timeout_config = aiohttp.ClientTimeout(total=timeout)

            # 使用aiohttp异步下载图片
            async with aiohttp.ClientSession(timeout=timeout_config) as session:
                async with session.get(image_url) as response:
                    response.raise_for_status()  # 检查HTTP错误

                    # 如果没有提供文件名，则自动生成一个
                    if filename is None:
                        # 尝试从URL或Content-Type获取文件扩展名
                        content_type = response.headers.get('content-type', '')
                        if 'jpeg' in content_type or 'jpg' in content_type:
                            extension = 'jpg'
                        elif 'png' in content_type:
                            extension = 'png'
                        elif 'webp' in content_type:
                            extension = 'webp'
                        else:
                            extension = 'jpg'  # 默认扩展名

                        filename = f"generated_image_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{extension}"

                    # 构建完整的文件路径
                    file_path = self.download_dir / filename

                    # 异步写入文件
                    with open(file_path, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            f.write(chunk)

                    print(f"✅ 图片下载成功: {file_path.absolute()}")
                    return str(file_path.absolute())

        except asyncio.TimeoutError:
            print(f"❌ 下载超时 ({timeout}秒)")
            return None
        except aiohttp.ClientError as e:
            print(f"❌ 下载失败: {e}")
            return None
        except Exception as e:
            print(f"❌ 保存文件时出错: {e}")
            return None

    async def prepare_image_data(self, image_path):
        """处理本地图片为API可接受的格式"""
        try:
            if not os.path.exists(image_path):
                raise FileNotFoundError(f"图片文件不存在: {image_path}")
            
            mime_type, _ = mimetypes.guess_type(image_path)
            if not mime_type or not mime_type.startswith('image/'):
                ext = os.path.splitext(image_path)[1].lower()
                if ext in ['.jpg', '.jpeg']:
                    mime_type = 'image/jpeg'
                elif ext == '.png':
                    mime_type = 'image/png'
                elif ext == '.webp':
                    mime_type = 'image/webp'
                else:
                    raise ValueError(f"不支持的图片格式: {ext}")
            
            with open(image_path, "rb") as image_file:
                image_data = image_file.read()
                encoded_string = base64.b64encode(image_data).decode('utf-8')
            
            return f"data:{mime_type};base64,{encoded_string}"
            
        except Exception as e:
            print(f"处理图片时出错: {e}")
            raise
    
    async def list_styles(self):
        """显示所有可用风格"""
        print("🎨 可用的动漫风格：")
        print("=" * 50)
        for key, style in self.styles.items():
            print(f"{key}: {style['name']}")
            print(f"   特点: {style['description']}")
            print()
    async def get_styles_key(self):
        """获取所有可用风格的键名"""
        return list(self.styles.keys())
    
    async def generate_image(self, image_path, style_key, custom_prompt="",
                             seed=123, guidance_scale=7.0, size="adaptive",
                             watermark=False, download_filename=None, **kwargs):
        """生成指定风格的图片

        Args:
            image_path (str): 输入图片路径
            style_key (str): 风格键名
            custom_prompt (str): 自定义提示词
            seed (int): 随机种子，控制生成结果的一致性
            guidance_scale (float): 引导强度，控制对提示词的遵循程度
            size (str): 输出尺寸，只支持"adaptive"自适应
            watermark (bool): 是否添加水印
            download_filename (str, optional): 自定义下载文件名，仅在启用下载时有效
            **kwargs: 其他API参数

        Returns:
            dict: 包含生成结果的字典，包含以下键：
                - response: API响应对象
                - image_url: 生成的图片URL
                - local_path: 本地文件路径（如果启用下载）
        """
        if style_key not in self.styles:
            raise ValueError(f"不支持的风格: {style_key}")
        
        # 验证size参数
        if size != "adaptive":
            print(f"⚠️  警告: size参数只支持'adaptive'，已自动修正为'adaptive'")
            size = "adaptive"
        
        style = self.styles[style_key]
        
        # 组合提示词
        if custom_prompt:
            full_prompt = f"{style['prompt']}，{custom_prompt}"
        else:
            full_prompt = style['prompt']
        
        # 准备图片数据
        image_data = await self.prepare_image_data(image_path)
        
        # 设置参数
        params = {
            "model": os.environ.get("ARK_MODEL"),
            "prompt": full_prompt,
            "image": image_data,
            "seed": seed,
            "guidance_scale": guidance_scale,
            "size": size,
            "watermark": watermark
        }
        
        # 更新用户自定义参数
        params.update(kwargs)
        
        try:
            print(f"🎨 正在生成 {style['name']} 风格...")
            print(f"📝 提示词: {full_prompt}")
            print(f"🎲 随机种子: {seed}")
            print(f"🎯 引导强度: {guidance_scale}")
            print(f"📐 输出尺寸: {size}")
            
            response = self.client.images.generate(**params)

            print("✅ 生成成功!")
            image_url = response.data[0].url
            print(f"🖼️  图片URL: {image_url}")

            # 构建返回结果
            result = {
                'response': response,
                'image_url': image_url,
                'local_path': None
            }

            # 如果启用了下载功能，则异步下载图片
            if self.download_images:
                # 生成文件名（如果用户没有提供）
                if download_filename is None:
                    download_filename = self._generate_filename(style_key, seed)

                # 异步下载图片
                local_path = await self.download_image(image_url, download_filename)
                result['local_path'] = local_path

                if local_path:
                    print(f"💾 本地文件: {local_path}")

            return result
            
        except Exception as e:
            print(f"❌ 生成失败: {e}")
            raise

    def set_download_config(self, download_images=None, download_dir=None):
        """
        动态设置下载配置

        Args:
            download_images (bool, optional): 是否启用图片下载
            download_dir (str, optional): 下载目录路径
        """
        if download_images is not None:
            self.download_images = download_images
            print(f"📥 图片下载功能: {'启用' if download_images else '禁用'}")

        if download_dir is not None:
            self.download_dir = Path(download_dir)
            print(f"📁 下载目录设置为: {self.download_dir.absolute()}")

        # 如果启用下载功能，确保目录存在
        if self.download_images:
            self._ensure_download_directory()

    def get_download_config(self):
        """
        获取当前下载配置

        Returns:
            dict: 包含下载配置的字典
        """
        return {
            'download_enabled': self.download_images,
            'download_directory': str(self.download_dir.absolute()) if self.download_images else None
        }

async def main():
    """异步主函数示例"""
    # 示例1: 不启用下载功能（默认行为）
    print("=== 示例1: 仅生成图片URL ===")
    generator = AnimeStyleGenerator()

    # 显示所有风格
    await generator.list_styles()

    # 显示当前下载配置
    config = generator.get_download_config()
    print(f"当前下载配置: {config}")

    # 生成图片（仅返回URL）
    image_path = "苏兆强.jpg"

    try:
        result = await generator.generate_image(
            image_path=image_path,
            style_key="ghibli",
            custom_prompt="保持人物特征，人物整体发型，面部轮廓，五官保持不变",
            seed=456,
            guidance_scale=10.0,
            watermark=False
        )
        print(f"生成结果: URL={result['image_url']}, 本地路径={result['local_path']}")

    except Exception as e:
        print(f"生成过程出错: {e}")

    print("\n" + "="*60 + "\n")

    # 示例2: 启用下载功能
    print("=== 示例2: 启用图片下载功能 ===")
    generator_with_download = AnimeStyleGenerator(
        download_images=True,
        download_dir="my_anime_images"
    )

    try:
        # 生成并下载赛博朋克风格
        result = await generator_with_download.generate_image(
            image_path=image_path,
            style_key="cyberpunk",
            custom_prompt="保持人物特征，添加科技感元素",
            seed=789,
            guidance_scale=6.5,
            download_filename="cyberpunk_style.jpg"  # 自定义文件名
        )
        print(f"生成结果: URL={result['image_url']}, 本地路径={result['local_path']}")

        # 动态切换下载设置
        generator_with_download.set_download_config(download_images=False)

        # 这次不会下载
        result2 = await generator_with_download.generate_image(
            image_path=image_path,
            style_key="chibi",
            custom_prompt="超级可爱的表情",
            seed=999
        )
        print(f"生成结果: URL={result2['image_url']}, 本地路径={result2['local_path']}")

    except Exception as e:
        print(f"生成过程出错: {e}")

# 使用示例
if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())
