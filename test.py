import asyncio
from async_video_generator import AsyncVideoGenerator

async def main():
    generator = AsyncVideoGenerator()

    # 基于首尾帧生成视频
    result = await generator.generate_text_to_video(
        prompt="春天的樱花在微风中飞舞，粉色花瓣缓缓飘落,一只哥斯拉坐在树下，看着镜头跳舞。",
        verbose=True
    )
    print(f"视频URL: {result.video_url}")
    status = await generator.get_task_status(result.task_id)
    print(f"任务状态: {status}")

if __name__ == "__main__":
    asyncio.run(main())
