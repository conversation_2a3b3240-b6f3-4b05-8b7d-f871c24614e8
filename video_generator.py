import os
import time
import base64
import mimetypes
from pathlib import Path
from typing import Optional, List, Dict, Any
from dataclasses import dataclass
from dotenv import load_dotenv
# 通过 pip install 'volcengine-python-sdk[ark]' 安装方舟SDK
from volcenginesdkarkruntime import Ark


@dataclass
class VideoGenerationConfig:
    """视频生成配置类

    支持从环境变量自动加载配置，环境变量名格式：
    - ARK_VIDEO_MODEL
    - ARK_VIDEO_RESOLUTION
    - ARK_VIDEO_DURATION
    - ARK_VIDEO_RATIO
    - ARK_VIDEO_FRAMESPERSECOND
    - ARK_VIDEO_SEED
    """
    model: str
    resolution: str = "1080p"  # 分辨率：720p, 1080p
    duration: int = 5  # 视频时长（秒）
    ratio: str = "adaptive"  # 宽高比：adaptive, 16:9, 9:16, 1:1 (某些模型只支持adaptive)
    framespersecond: int = 24  # 帧率
    seed: Optional[int] = None  # 随机种子，用于复现结果

    @classmethod
    def from_env(cls, **overrides) -> 'VideoGenerationConfig':
        """从环境变量创建配置

        自动加载 .env 文件并从环境变量读取配置参数

        Args:
            **overrides: 覆盖环境变量的参数

        Returns:
            VideoGenerationConfig: 配置实例

        Example:
            # 使用环境变量的默认配置
            config = VideoGenerationConfig.from_env()

            # 使用环境变量但覆盖某些参数
            config = VideoGenerationConfig.from_env(duration=10, resolution="720p")
        """
        # 确保加载 .env 文件
        load_dotenv()

        # 从环境变量读取配置
        model = os.environ.get("ARK_VIDEO_MODEL")
        if model is None:
            raise ValueError(
                "ARK_VIDEO_MODEL 环境变量未设置！请在 .env 文件或系统环境变量中设置模型ID。\n"
                "推荐模型：\n"
                "- 文生视频/图生视频：doubao-seedance-1-0-pro-250528\n"
                "- 首尾帧视频：wan2-1-14b-flf2v-250417"
            )
        resolution = os.environ.get("ARK_VIDEO_RESOLUTION", "1080p")
        duration = int(os.environ.get("ARK_VIDEO_DURATION", "5"))
        ratio = os.environ.get("ARK_VIDEO_RATIO", "adaptive")
        framespersecond = int(os.environ.get("ARK_VIDEO_FRAMESPERSECOND", "24"))
        seed_str = os.environ.get("ARK_VIDEO_SEED")
        seed = int(seed_str) if seed_str else None

        # 应用覆盖参数
        config_params = {
            "model": model,
            "resolution": resolution,
            "duration": duration,
            "ratio": ratio,
            "framespersecond": framespersecond,
            "seed": seed
        }
        config_params.update(overrides)

        return cls(**config_params)




@dataclass
class TaskResult:
    """任务结果类"""
    task_id: str
    status: str
    model: Optional[str] = None
    video_url: Optional[str] = None
    error: Optional[str] = None
    usage: Optional[Dict[str, Any]] = None
    created_at: Optional[int] = None
    updated_at: Optional[int] = None
    seed: Optional[int] = None
    revised_prompt: Optional[str] = None


class VideoGenerator:
    """视频生成处理类

    这个类封装了火山引擎视频生成API的所有功能，包括：
    - 创建视频生成任务
    - 轮询任务状态
    - 获取任务详情
    - 列出任务
    - 删除任务
    """

    def __init__(self, base_url: Optional[str] = None, api_key: Optional[str] = None,
                 default_config: Optional[VideoGenerationConfig] = None):
        """初始化视频生成器

        Args:
            base_url: API基础URL，如果为None则从环境变量ARK_BASE_URL读取
            api_key: API密钥，如果为None则从环境变量ARK_API_KEY读取
            default_config: 默认配置，如果为None则自动从环境变量加载
        """
        load_dotenv()

        self.client = Ark(
            base_url=base_url or os.environ.get("ARK_BASE_URL"),
            api_key=api_key or os.environ.get("ARK_API_KEY"),
        )

        # 自动加载默认配置
        if default_config is None:
            self.default_config = VideoGenerationConfig.from_env()
        else:
            self.default_config = default_config

        # 保持向后兼容
        self.default_model = self.default_config.model

    def create_video_task(self,
                         prompt: str,
                         config: Optional[VideoGenerationConfig] = None,
                         image_url: Optional[str] = None,
                         first_frame_url: Optional[str] = None,
                         last_frame_url: Optional[str] = None) -> TaskResult:
        """创建视频生成任务

        Args:
            prompt: 文本提示词，描述要生成的视频内容
            config: 视频生成配置，如果为None则使用默认配置
            image_url: 可选的首帧图片URL或本地文件路径，用于图生视频（兼容旧接口）
                      支持网络URL (http://..., https://...) 和本地文件路径
                      本地文件会自动转换为base64编码
            first_frame_url: 可选的首帧图片URL或本地文件路径，用于首尾帧视频生成
            last_frame_url: 可选的尾帧图片URL或本地文件路径，用于首尾帧视频生成

        Returns:
            TaskResult: 包含任务ID和初始状态的结果对象

        Raises:
            Exception: 当API调用失败时抛出异常
        """
        if config is None:
            config = self.default_config

        try:
            # 构建完整的提示词，包含视频参数
            full_prompt = self._build_prompt_with_params(prompt, config)

            # 构建内容列表
            content = [
                {
                    "type": "text",
                    "text": full_prompt
                }
            ]

            # 处理图片输入（支持多种模式）
            self._add_image_content(content, image_url, first_frame_url, last_frame_url)

            # 构建请求参数
            request_params = {
                "model": config.model,
                "content": content
            }

            result = self.client.content_generation.tasks.create(**request_params)

            return TaskResult(
                task_id=result.id,
                status=result.status if hasattr(result, 'status') else 'created',
                model=config.model
            )

        except Exception as e:
            raise Exception(f"创建视频生成任务失败: {str(e)}")

    def _build_prompt_with_params(self, prompt: str, config: VideoGenerationConfig) -> str:
        """构建包含参数的完整提示词

        Args:
            prompt: 基础提示词
            config: 视频生成配置

        Returns:
            str: 包含参数的完整提示词
        """
        # 基础提示词
        full_prompt = prompt

        # 添加参数到提示词中
        params = []

        if config.resolution:
            params.append(f"--rs {config.resolution}")

        if config.ratio:
            params.append(f"--rt {config.ratio}")

        if config.duration:
            params.append(f"--dur {config.duration}")

        if config.framespersecond:
            params.append(f"--fps {config.framespersecond}")

        if config.seed is not None:
            params.append(f"--seed {config.seed}")

        # 将参数添加到提示词末尾
        if params:
            full_prompt += " " + " ".join(params)

        return full_prompt

    def _process_image_input(self, image_input: str) -> str:
        """处理图片输入，支持URL和本地文件路径

        Args:
            image_input: 图片URL或本地文件路径

        Returns:
            str: 处理后的图片URL（网络URL或base64 data URL）

        Raises:
            Exception: 当文件不存在或格式不支持时抛出异常
        """
        # 检查是否是网络URL
        if image_input.startswith(('http://', 'https://')):
            return image_input

        # 处理本地文件
        file_path = Path(image_input)

        if not file_path.exists():
            raise Exception(f"图片文件不存在: {image_input}")

        if not file_path.is_file():
            raise Exception(f"路径不是文件: {image_input}")

        # 检查文件类型
        mime_type, _ = mimetypes.guess_type(str(file_path))
        if not mime_type or not mime_type.startswith('image/'):
            raise Exception(f"不支持的图片格式: {image_input}")

        try:
            # 读取文件并转换为base64
            with open(file_path, 'rb') as f:
                image_data = f.read()

            # 编码为base64
            base64_data = base64.b64encode(image_data).decode('utf-8')

            # 构建data URL
            data_url = f"data:{mime_type};base64,{base64_data}"

            return data_url

        except Exception as e:
            raise Exception(f"读取图片文件失败: {str(e)}")

    def _add_image_content(self,
                          content: List[Dict[str, Any]],
                          image_url: Optional[str] = None,
                          first_frame_url: Optional[str] = None,
                          last_frame_url: Optional[str] = None) -> None:
        """添加图片内容到请求中

        Args:
            content: 内容列表，将在此基础上添加图片
            image_url: 兼容旧接口的图片URL（作为首帧）
            first_frame_url: 首帧图片URL
            last_frame_url: 尾帧图片URL
        """
        # 处理兼容性：如果使用了旧的image_url参数，将其作为首帧
        if image_url and not first_frame_url:
            first_frame_url = image_url

        # 添加首帧图片
        if first_frame_url:
            processed_first_frame = self._process_image_input(first_frame_url)
            content.append({
                "type": "image_url",
                "image_url": {
                    "url": processed_first_frame
                },
                "role": "first_frame"
            })

        # 添加尾帧图片
        if last_frame_url:
            processed_last_frame = self._process_image_input(last_frame_url)
            content.append({
                "type": "image_url",
                "image_url": {
                    "url": processed_last_frame
                },
                "role": "last_frame"
            })

    def get_task_status(self, task_id: str) -> TaskResult:
        """获取任务状态和详情

        Args:
            task_id: 任务ID

        Returns:
            TaskResult: 包含任务详细信息的结果对象

        Raises:
            Exception: 当API调用失败时抛出异常
        """
        try:
            result = self.client.content_generation.tasks.get(task_id=task_id)

            return TaskResult(
                task_id=result.id,
                status=result.status,
                model=getattr(result, 'model', None),
                video_url=result.content.video_url if result.content else None,
                error=result.error,
                usage=result.usage.__dict__ if result.usage else None,
                created_at=result.created_at,
                updated_at=result.updated_at,
                seed=result.seed,
                revised_prompt=result.revised_prompt
            )

        except Exception as e:
            raise Exception(f"获取任务状态失败: {str(e)}")

    def wait_for_completion(self,
                           task_id: str,
                           poll_interval: int = 10,
                           max_wait_time: int = 600,
                           verbose: bool = True) -> TaskResult:
        """等待任务完成

        Args:
            task_id: 任务ID
            poll_interval: 轮询间隔（秒），默认10秒
            max_wait_time: 最大等待时间（秒），默认600秒（10分钟）
            verbose: 是否打印轮询状态，默认True

        Returns:
            TaskResult: 最终的任务结果

        Raises:
            Exception: 当任务失败或超时时抛出异常
        """
        start_time = time.time()

        while True:
            # 检查是否超时
            if time.time() - start_time > max_wait_time:
                raise Exception(f"任务等待超时，超过{max_wait_time}秒")

            result = self.get_task_status(task_id)

            if result.status == "succeeded":
                if verbose:
                    print("任务成功完成！")
                return result
            elif result.status == "failed":
                error_msg = result.error or "未知错误"
                raise Exception(f"任务失败: {error_msg}")
            elif result.status in ["cancelled"]:
                raise Exception(f"任务被取消")
            else:
                if verbose:
                    print(f"当前状态: {result.status}, {poll_interval}秒后重试...")
                time.sleep(poll_interval)

    def list_tasks(self,
                   page_num: int = 1,
                   page_size: int = 10,
                   status: Optional[str] = None,
                   model: Optional[str] = None,
                   task_ids: Optional[List[str]] = None) -> Dict[str, Any]:
        """列出任务

        Args:
            page_num: 页码，默认1
            page_size: 每页大小，默认10
            status: 按状态筛选，可选值：queued, running, succeeded, failed, cancelled
            model: 按模型筛选
            task_ids: 按任务ID列表筛选

        Returns:
            Dict: 包含任务列表和总数的字典

        Raises:
            Exception: 当API调用失败时抛出异常
        """
        try:
            params = {
                "page_num": page_num,
                "page_size": page_size
            }

            if status:
                params["status"] = status
            if model:
                params["model"] = model
            if task_ids:
                params["task_ids"] = task_ids

            result = self.client.content_generation.tasks.list(**params)

            return {
                "total": result.total,
                "items": [
                    {
                        "task_id": item.id,
                        "status": item.status,
                        "model": item.model,
                        "created_at": item.created_at,
                        "updated_at": item.updated_at
                    }
                    for item in result.items
                ]
            }

        except Exception as e:
            raise Exception(f"列出任务失败: {str(e)}")

    def delete_task(self, task_id: str) -> bool:
        """删除任务

        Args:
            task_id: 要删除的任务ID

        Returns:
            bool: 删除是否成功

        Raises:
            Exception: 当API调用失败时抛出异常
        """
        try:
            self.client.content_generation.tasks.delete(task_id=task_id)
            return True
        except Exception as e:
            raise Exception(f"删除任务失败: {str(e)}")

    def generate_video(self,
                      prompt: str,
                      config: Optional[VideoGenerationConfig] = None,
                      image_url: Optional[str] = None,
                      first_frame_url: Optional[str] = None,
                      last_frame_url: Optional[str] = None,
                      wait_for_completion: bool = True,
                      poll_interval: int = 10,
                      max_wait_time: int = 600,
                      verbose: bool = True) -> TaskResult:
        """一站式视频生成方法

        这个方法将创建任务和等待完成合并为一个操作，简化使用流程。
        支持文生视频、图生视频和首尾帧视频生成三种模式。

        Args:
            prompt: 文本提示词
            config: 视频生成配置
            image_url: 可选的首帧图片URL，用于图生视频（兼容旧接口）
            first_frame_url: 可选的首帧图片URL，用于首尾帧视频生成
            last_frame_url: 可选的尾帧图片URL，用于首尾帧视频生成
            wait_for_completion: 是否等待任务完成，默认True
            poll_interval: 轮询间隔（秒）
            max_wait_time: 最大等待时间（秒）
            verbose: 是否打印详细信息

        Returns:
            TaskResult: 任务结果，如果wait_for_completion为True则返回最终结果
        """
        if verbose:
            # 确定视频生成类型
            if first_frame_url and last_frame_url:
                video_type = "首尾帧视频"
                print(f"开始生成{video_type}: {prompt}")
                print(f"首帧图片: {first_frame_url}")
                print(f"尾帧图片: {last_frame_url}")
            elif image_url or first_frame_url:
                video_type = "图生视频"
                print(f"开始生成{video_type}: {prompt}")
                print(f"首帧图片: {image_url or first_frame_url}")
            else:
                video_type = "文生视频"
                print(f"开始生成{video_type}: {prompt}")

        # 创建任务
        result = self.create_video_task(prompt, config, image_url, first_frame_url, last_frame_url)

        if verbose:
            print(f"任务已创建，ID: {result.task_id}")

        if wait_for_completion:
            # 等待完成
            result = self.wait_for_completion(
                result.task_id,
                poll_interval=poll_interval,
                max_wait_time=max_wait_time,
                verbose=verbose
            )

            if verbose and result.video_url:
                print(f"视频生成完成！下载链接: {result.video_url}")

        return result

    def generate_text_to_video(self,
                              prompt: str,
                              config: Optional[VideoGenerationConfig] = None,
                              wait_for_completion: bool = True,
                              poll_interval: int = 10,
                              max_wait_time: int = 600,
                              verbose: bool = True) -> TaskResult:
        """文生视频专用方法

        Args:
            prompt: 文本提示词
            config: 视频生成配置
            wait_for_completion: 是否等待任务完成
            poll_interval: 轮询间隔（秒）
            max_wait_time: 最大等待时间（秒）
            verbose: 是否打印详细信息

        Returns:
            TaskResult: 任务结果
        """
        return self.generate_video(
            prompt=prompt,
            config=config,
            image_url=None,
            wait_for_completion=wait_for_completion,
            poll_interval=poll_interval,
            max_wait_time=max_wait_time,
            verbose=verbose
        )

    def generate_image_to_video(self,
                               prompt: str,
                               image_url: str,
                               config: Optional[VideoGenerationConfig] = None,
                               wait_for_completion: bool = True,
                               poll_interval: int = 10,
                               max_wait_time: int = 600,
                               verbose: bool = True) -> TaskResult:
        """图生视频专用方法

        Args:
            prompt: 文本提示词，描述视频中的动作和变化
            image_url: 首帧图片的URL
            config: 视频生成配置
            wait_for_completion: 是否等待任务完成
            poll_interval: 轮询间隔（秒）
            max_wait_time: 最大等待时间（秒）
            verbose: 是否打印详细信息

        Returns:
            TaskResult: 任务结果
        """
        if not image_url:
            raise ValueError("图生视频必须提供image_url参数")

        return self.generate_video(
            prompt=prompt,
            config=config,
            image_url=image_url,
            wait_for_completion=wait_for_completion,
            poll_interval=poll_interval,
            max_wait_time=max_wait_time,
            verbose=verbose
        )

    def generate_first_last_frame_video(self,
                                       prompt: str,
                                       first_frame_url: str,
                                       last_frame_url: str,
                                       config: Optional[VideoGenerationConfig] = None,
                                       wait_for_completion: bool = True,
                                       poll_interval: int = 10,
                                       max_wait_time: int = 600,
                                       verbose: bool = True) -> TaskResult:
        """首尾帧视频生成专用方法

        Args:
            prompt: 文本提示词，描述视频中的动作和变化
            first_frame_url: 首帧图片的URL或本地文件路径
            last_frame_url: 尾帧图片的URL或本地文件路径
            config: 视频生成配置
            wait_for_completion: 是否等待任务完成
            poll_interval: 轮询间隔（秒）
            max_wait_time: 最大等待时间（秒）
            verbose: 是否打印详细信息

        Returns:
            TaskResult: 任务结果
        """
        if not first_frame_url or not last_frame_url:
            raise ValueError("首尾帧视频生成必须同时提供first_frame_url和last_frame_url参数")

        return self.generate_video(
            prompt=prompt,
            config=config,
            first_frame_url=first_frame_url,
            last_frame_url=last_frame_url,
            wait_for_completion=wait_for_completion,
            poll_interval=poll_interval,
            max_wait_time=max_wait_time,
            verbose=verbose
        )


def main():
    """示例用法"""
    # 创建视频生成器实例
    generator = VideoGenerator()

    # 配置视频参数
    config = VideoGenerationConfig(
        model=os.environ.get("ARK_VIDEO_MODEL"),
        resolution="1080p",
        duration=5,
        ratio="16:9",
        framespersecond=24
    )

    try:
        # 生成视频
        result = generator.generate_video(
            prompt="爱情公寓关谷神奇在公寓里使用武士刀表演滑稽的切腹",
            config=config,
            verbose=True
        )

        print(f"\n最终结果:")
        print(f"任务ID: {result.task_id}")
        print(f"状态: {result.status}")
        if result.video_url:
            print(f"视频URL: {result.video_url}")
        if result.usage:
            print(f"使用情况: {result.usage}")

        # 演示其他功能
        print("\n----- 列出任务 -----")
        tasks = generator.list_tasks(status="succeeded", page_size=5)
        print(f"总任务数: {tasks['total']}")
        for task in tasks['items']:
            print(f"任务: {task['task_id']}, 状态: {task['status']}")

        # 删除任务
        print(f"\n----- 删除任务 -----")
        if generator.delete_task(result.task_id):
            print(f"任务 {result.task_id} 已删除")

    except Exception as e:
        print(f"错误: {e}")


if __name__ == "__main__":
    main()
