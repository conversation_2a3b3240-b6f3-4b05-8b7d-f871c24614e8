#!/usr/bin/env python3
"""
测试视频生成器类的功能
"""

from video_generator import VideoGenerator, VideoGenerationConfig
import os
from dotenv import load_dotenv

# 确保加载环境变量
load_dotenv()


def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试基本功能 ===")
    
    try:
        # 创建视频生成器
        generator = VideoGenerator()
        print("✓ VideoGenerator 初始化成功")
        
        # 创建配置
        config = VideoGenerationConfig(
            model=os.environ.get("ARK_VIDEO_MODEL"),
            resolution="1080p",
            duration=5,
            ratio="16:9",
            framespersecond=24
        )
        print("✓ VideoGenerationConfig 创建成功")
        
        # 测试创建任务（不等待完成）
        print("\n测试创建任务...")
        result = generator.create_video_task(
            prompt="一只可爱的小猫在阳光下玩耍",
            config=config
        )
        print(f"✓ 任务创建成功，ID: {result.task_id}")
        
        # 测试获取任务状态
        print("\n测试获取任务状态...")
        status = generator.get_task_status(result.task_id)
        print(f"✓ 任务状态: {status.status}")
        
        # 测试列出任务
        print("\n测试列出任务...")
        tasks = generator.list_tasks(page_size=3)
        print(f"✓ 找到 {tasks['total']} 个任务")
        
        # 如果任务还在运行，等待一段时间
        if status.status in ["queued", "running"]:
            print(f"\n任务正在处理中，等待完成...")
            try:
                final_result = generator.wait_for_completion(
                    result.task_id,
                    poll_interval=10,
                    max_wait_time=120,  # 只等待2分钟
                    verbose=True
                )
                print(f"✓ 任务完成！状态: {final_result.status}")
                if final_result.video_url:
                    print(f"✓ 视频URL: {final_result.video_url}")
            except Exception as e:
                print(f"⚠ 等待超时或失败: {e}")
        
        # 测试删除任务
        print(f"\n测试删除任务...")
        try:
            success = generator.delete_task(result.task_id)
            if success:
                print(f"✓ 任务 {result.task_id} 删除成功")
        except Exception as e:
            print(f"⚠ 删除任务失败: {e}")
        
        print("\n✓ 所有基本功能测试完成！")
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False
    
    return True


def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    generator = VideoGenerator()
    
    # 测试无效任务ID
    try:
        generator.get_task_status("invalid-task-id")
        print("✗ 应该抛出异常但没有")
    except Exception as e:
        print(f"✓ 正确处理无效任务ID: {type(e).__name__}")
    
    # 测试删除不存在的任务
    try:
        generator.delete_task("non-existent-task")
        print("✗ 应该抛出异常但没有")
    except Exception as e:
        print(f"✓ 正确处理删除不存在的任务: {type(e).__name__}")
    
    print("✓ 错误处理测试完成！")


def test_configuration():
    """测试配置功能"""
    print("\n=== 测试配置功能 ===")
    
    try:
        # 测试不同的配置
        configs = [
            VideoGenerationConfig(
                model=os.environ.get("ARK_VIDEO_MODEL"),
                resolution="720p",
                duration=3,
                ratio="9:16",
                framespersecond=30
            ),
            VideoGenerationConfig(
                model=os.environ.get("ARK_VIDEO_MODEL"),
                resolution="1080p",
                duration=8,
                ratio="1:1",
                framespersecond=24,
                seed=12345
            )
        ]
        
        for i, config in enumerate(configs):
            print(f"✓ 配置 {i+1} 创建成功: {config.resolution}, {config.ratio}")
        
        print("✓ 配置功能测试完成！")
        
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")


def main():
    """主测试函数"""
    print("开始测试视频生成器类...")
    print("=" * 50)
    
    # 检查环境变量
    required_env_vars = ["ARK_API_KEY", "ARK_BASE_URL", "ARK_VIDEO_MODEL"]
    missing_vars = [var for var in required_env_vars if not os.environ.get(var)]
    
    if missing_vars:
        print(f"✗ 缺少环境变量: {', '.join(missing_vars)}")
        print("请确保 .env 文件包含所有必需的环境变量")
        return
    
    print("✓ 环境变量检查通过")
    
    # 运行测试
    test_configuration()
    test_error_handling()
    
    # 最后运行基本功能测试（因为会创建实际任务）
    success = test_basic_functionality()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 所有测试通过！视频生成器类工作正常。")
    else:
        print("❌ 部分测试失败，请检查配置和网络连接。")


if __name__ == "__main__":
    main()
