#!/usr/bin/env python3
"""
测试环境变量优先级的脚本
"""

import os
from dotenv import load_dotenv

def test_env_priority():
    """测试环境变量优先级"""
    
    print("🧪 环境变量优先级测试")
    print("=" * 50)
    
    # 显示加载 .env 之前的环境变量
    print("\n📋 加载 .env 文件之前:")
    print(f"ARK_API_KEY = {os.environ.get('ARK_API_KEY', '未设置')}")
    print(f"ARK_BASE_URL = {os.environ.get('ARK_BASE_URL', '未设置')}")
    
    # 加载 .env 文件（默认不覆盖）
    load_dotenv()
    
    print("\n📋 加载 .env 文件之后:")
    print(f"ARK_API_KEY = {os.environ.get('ARK_API_KEY', '未设置')}")
    print(f"ARK_BASE_URL = {os.environ.get('ARK_BASE_URL', '未设置')}")
    
    print("\n💡 测试说明:")
    print("1. 如果命令行设置了 ARK_API_KEY，它不会被 .env 文件覆盖")
    print("2. 如果命令行没有设置某个变量，会使用 .env 文件中的值")
    print("3. 这是 python-dotenv 的默认安全行为")
    
    print("\n🔧 测试命令:")
    print("# 不设置环境变量运行:")
    print("python test_env_priority.py")
    print()
    print("# 设置环境变量后运行:")
    print("export ARK_API_KEY=my_command_line_key")
    print("python test_env_priority.py")

if __name__ == "__main__":
    test_env_priority()
