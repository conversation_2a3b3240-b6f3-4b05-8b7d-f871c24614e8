#!/usr/bin/env python3
"""
测试本地图片文件处理功能

这个脚本演示如何使用本地图片文件进行图生视频
"""

from video_generator import VideoGenerator, VideoGenerationConfig
import os
import aiohttp
import asyncio
from dotenv import load_dotenv

# 确保加载环境变量
load_dotenv()


async def download_test_image():
    """下载测试图片到本地"""
    print("📥 下载测试图片...")

    # 使用火山引擎文档中的示例图片
    image_url = "https://ark-project.tos-cn-beijing.volces.com/doc_image/i2v_foxrgirl.png"
    local_path = "test_image.png"

    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(image_url) as response:
                response.raise_for_status()

                with open(local_path, 'wb') as f:
                    f.write(await response.read())

        print(f"✅ 图片已下载到: {local_path}")
        return local_path

    except Exception as e:
        print(f"❌ 下载图片失败: {e}")
        return None


async def test_local_image_processing():
    """测试本地图片处理功能"""
    print("\n=== 测试本地图片处理功能 ===")

    generator = VideoGenerator()

    # 下载测试图片
    local_image_path = await download_test_image()
    if not local_image_path:
        print("❌ 无法获取测试图片，跳过本地图片测试")
        return None
    
    try:
        # 测试图片处理方法
        print("🔍 测试图片处理方法...")
        processed_url = generator._process_image_input(local_image_path)
        
        print(f"✅ 本地图片处理成功")
        print(f"📁 原始路径: {local_image_path}")
        print(f"🔗 处理后URL: {processed_url[:100]}...")  # 只显示前100个字符
        
        # 验证是否是base64格式
        if processed_url.startswith("data:image/"):
            print("✅ 正确生成了base64 data URL")
        else:
            print("❌ 生成的URL格式不正确")
        
        return local_image_path
        
    except Exception as e:
        print(f"❌ 本地图片处理失败: {e}")
        return None


def test_local_image_to_video():
    """测试使用本地图片生成视频"""
    print("\n=== 测试本地图片生成视频 ===")
    
    generator = VideoGenerator()
    
    # 获取本地图片路径
    local_image_path = "test_image.png"
    
    if not os.path.exists(local_image_path):
        print("❌ 测试图片不存在，请先运行图片处理测试")
        return None
    
    # 创建配置
    config = VideoGenerationConfig(
        model=os.environ.get("ARK_VIDEO_MODEL"),
        resolution="1080p",
        duration=5,
        ratio="adaptive",
        framespersecond=24
    )
    
    try:
        print(f"📸 使用本地图片: {local_image_path}")
        print("🎬 开始生成图生视频...")
        
        # 使用本地图片生成视频
        result = generator.generate_image_to_video(
            prompt="女孩睁开眼睛，温柔地微笑，狐狸摇摇尾巴，背景中的光线柔和地变化",
            image_url=local_image_path,  # 使用本地文件路径
            config=config,
            verbose=True
        )
        
        print(f"✅ 本地图片生成视频成功！")
        print(f"🆔 任务ID: {result.task_id}")
        print(f"📹 视频URL: {result.video_url}")
        
        return result.task_id
        
    except Exception as e:
        print(f"❌ 本地图片生成视频失败: {e}")
        return None


def test_url_vs_local_comparison():
    """对比网络URL和本地文件的处理"""
    print("\n=== 对比网络URL和本地文件处理 ===")
    
    generator = VideoGenerator()
    
    # 网络URL
    network_url = "https://ark-project.tos-cn-beijing.volces.com/doc_image/i2v_foxrgirl.png"
    local_path = "test_image.png"
    
    try:
        print("🌐 处理网络URL...")
        processed_network = generator._process_image_input(network_url)
        print(f"✅ 网络URL处理结果: {processed_network}")
        
        if os.path.exists(local_path):
            print("📁 处理本地文件...")
            processed_local = generator._process_image_input(local_path)
            print(f"✅ 本地文件处理结果: {processed_local[:100]}...")
            
            # 比较处理方式
            print("\n📊 处理方式对比:")
            print(f"  网络URL: 直接使用原URL")
            print(f"  本地文件: 转换为base64 data URL")
        else:
            print("⚠️ 本地测试图片不存在")
        
    except Exception as e:
        print(f"❌ 对比测试失败: {e}")


def test_invalid_inputs():
    """测试无效输入的处理"""
    print("\n=== 测试无效输入处理 ===")
    
    generator = VideoGenerator()
    
    # 测试不存在的文件
    try:
        generator._process_image_input("non_existent_file.jpg")
        print("❌ 应该抛出异常但没有")
    except Exception as e:
        print(f"✅ 正确处理不存在的文件: {type(e).__name__}")
    
    # 测试非图片文件（如果存在的话）
    try:
        # 创建一个临时的非图片文件
        with open("test_text.txt", "w") as f:
            f.write("这不是图片文件")
        
        generator._process_image_input("test_text.txt")
        print("❌ 应该抛出异常但没有")
    except Exception as e:
        print(f"✅ 正确处理非图片文件: {type(e).__name__}")
    finally:
        # 清理临时文件
        if os.path.exists("test_text.txt"):
            os.remove("test_text.txt")


def cleanup():
    """清理测试文件"""
    print("\n🧹 清理测试文件...")
    
    files_to_remove = ["test_image.png", "test_text.txt"]
    
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"✅ 已删除: {file_path}")
            except Exception as e:
                print(f"⚠️ 删除失败 {file_path}: {e}")


async def main():
    """主测试函数"""
    print("🖼️ 本地图片处理功能测试")
    print("=" * 60)

    # 检查环境变量
    required_env_vars = ["ARK_API_KEY", "ARK_BASE_URL", "ARK_VIDEO_MODEL"]
    missing_vars = [var for var in required_env_vars if not os.environ.get(var)]

    if missing_vars:
        print(f"❌ 缺少环境变量: {', '.join(missing_vars)}")
        print("请确保 .env 文件包含所有必需的环境变量")
        return

    print("✅ 环境变量检查通过")

    # 收集生成的任务ID
    task_ids = []

    try:
        # 运行测试
        await test_local_image_processing()
        test_url_vs_local_comparison()
        test_invalid_inputs()
        
        # 测试实际视频生成（可选，因为会消耗API配额）
        print("\n❓ 是否要测试实际的本地图片视频生成？(y/n): ", end="")
        # 在自动化测试中，我们跳过用户输入
        # user_input = input().lower().strip()
        user_input = "n"  # 默认不执行，避免消耗过多API配额
        
        if user_input == "y":
            task_id = test_local_image_to_video()
            if task_id:
                task_ids.append(task_id)
        else:
            print("⏭️ 跳过实际视频生成测试")
        
        # 清理任务
        if task_ids:
            print(f"\n🧹 清理测试任务...")
            generator = VideoGenerator()
            for task_id in task_ids:
                try:
                    generator.delete_task(task_id)
                    print(f"✅ 已删除任务: {task_id}")
                except Exception as e:
                    print(f"⚠️ 删除任务失败 {task_id}: {e}")
        
    finally:
        # 清理测试文件
        cleanup()
    
    print("\n" + "=" * 60)
    print("🎉 本地图片处理功能测试完成！")
    print("💡 提示: 现在支持网络URL和本地文件路径")
    print("📖 提示: 本地图片会自动转换为base64编码")


if __name__ == "__main__":
    asyncio.run(main())
