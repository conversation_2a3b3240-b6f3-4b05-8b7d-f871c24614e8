#!/usr/bin/env python3
"""
测试AsyncVideoGenerator异步视频生成器

这个脚本演示异步视频生成器的各种功能
"""

import asyncio
import os
import aiohttp
from dotenv import load_dotenv
from async_video_generator import AsyncVideoGenerator
from video_generator import VideoGenerationConfig

# 确保加载环境变量
load_dotenv()


async def download_test_image():
    """下载测试图片"""
    print("📥 下载测试图片...")
    
    image_url = "https://ark-project.tos-cn-beijing.volces.com/doc_image/i2v_foxrgirl.png"
    local_path = "async_test_image.png"
    
    if os.path.exists(local_path):
        print(f"✅ 测试图片已存在: {local_path}")
        return local_path
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(image_url) as response:
                response.raise_for_status()
                
                with open(local_path, 'wb') as f:
                    f.write(await response.read())
        
        print(f"✅ 测试图片已下载: {local_path}")
        return local_path
        
    except Exception as e:
        print(f"❌ 下载测试图片失败: {e}")
        return None


async def test_basic_async_operations():
    """测试基本异步操作"""
    print("\n=== 测试基本异步操作 ===")
    
    generator = AsyncVideoGenerator()
    
    try:
        # 测试异步创建任务
        print("🚀 异步创建任务...")
        result = await generator.create_video_task(
            prompt="春天的樱花在微风中飞舞，阳光透过花瓣洒在地面上"
        )
        print(f"✅ 任务创建成功: {result.task_id}")
        
        # 测试异步获取状态
        print("🔍 异步获取任务状态...")
        status = await generator.get_task_status(result.task_id)
        print(f"✅ 任务状态: {status.status}")
        
        # 测试异步等待完成（较短时间）
        print("⏳ 异步等待任务完成...")
        final_result = await generator.wait_for_completion(
            result.task_id,
            poll_interval=8,
            max_wait_time=120,  # 2分钟
            verbose=True
        )
        
        print(f"✅ 异步基本操作测试完成！")
        print(f"📹 视频URL: {final_result.video_url}")
        
        return result.task_id
        
    except Exception as e:
        print(f"❌ 基本异步操作测试失败: {e}")
        return None


async def test_async_image_to_video():
    """测试异步图生视频"""
    print("\n=== 测试异步图生视频 ===")
    
    # 下载测试图片
    local_image_path = await download_test_image()
    if not local_image_path:
        print("❌ 无法获取测试图片，跳过图生视频测试")
        return None
    
    generator = AsyncVideoGenerator()
    
    try:
        # 测试本地图片异步处理
        print("🖼️ 测试异步图片处理...")
        processed_url = await generator._process_image_input_async(local_image_path)
        print(f"✅ 图片处理成功，base64长度: {len(processed_url)}")
        
        # 测试异步图生视频
        print("🎬 异步生成图生视频...")
        result = await generator.generate_image_to_video(
            prompt="女孩睁开眼睛，温柔地微笑，狐狸摇摇尾巴",
            image_url=local_image_path,
            verbose=True
        )
        
        print(f"✅ 异步图生视频成功！")
        print(f"📹 视频URL: {result.video_url}")
        
        return result.task_id
        
    except Exception as e:
        print(f"❌ 异步图生视频测试失败: {e}")
        return None


async def test_concurrent_generation():
    """测试并发视频生成"""
    print("\n=== 测试并发视频生成 ===")
    
    generator = AsyncVideoGenerator()
    
    # 准备多个提示词
    prompts = [
        "春天的樱花飞舞",
        "夏日的海浪拍岸", 
        "秋天的枫叶飘落"
    ]
    
    try:
        print(f"🚀 开始并发生成 {len(prompts)} 个视频...")
        
        # 使用批量生成方法
        results = await generator.generate_videos_batch(
            prompts=prompts,
            max_concurrent=2,  # 限制并发数
            poll_interval=10,
            max_wait_time=180,  # 3分钟
            verbose=True
        )
        
        print(f"✅ 并发生成完成！成功生成 {len(results)} 个视频")
        
        task_ids = [result.task_id for result in results]
        return task_ids
        
    except Exception as e:
        print(f"❌ 并发生成测试失败: {e}")
        return []


async def test_async_task_management():
    """测试异步任务管理"""
    print("\n=== 测试异步任务管理 ===")
    
    generator = AsyncVideoGenerator()
    
    try:
        # 异步列出任务
        print("📋 异步列出任务...")
        tasks = await generator.list_tasks(
            status="succeeded",
            page_size=5
        )
        
        print(f"✅ 找到 {tasks['total']} 个成功的任务")
        for i, task in enumerate(tasks['items'][:3]):
            print(f"  {i+1}. 任务ID: {task['task_id'][:20]}...")
        
        # 异步列出正在运行的任务
        running_tasks = await generator.list_tasks(status="running")
        print(f"📊 正在运行的任务数: {running_tasks['total']}")
        
        print("✅ 异步任务管理测试完成！")
        
    except Exception as e:
        print(f"❌ 异步任务管理测试失败: {e}")


async def test_multiple_tasks_waiting():
    """测试多任务并发等待"""
    print("\n=== 测试多任务并发等待 ===")
    
    generator = AsyncVideoGenerator()
    
    try:
        # 创建多个任务但不等待完成
        print("🚀 创建多个任务...")
        task_ids = []
        
        prompts = [
            "宁静的湖面倒映着蓝天白云",
            "可爱的小猫在阳光下打盹"
        ]
        
        for i, prompt in enumerate(prompts):
            result = await generator.create_video_task(prompt)
            task_ids.append(result.task_id)
            print(f"✅ 任务 {i+1} 已创建: {result.task_id}")
        
        # 并发等待所有任务完成
        print("⏳ 并发等待所有任务完成...")
        results = await generator.wait_for_multiple_tasks(
            task_ids,
            poll_interval=10,
            max_wait_time=180,  # 3分钟
            verbose=True
        )
        
        print(f"✅ 多任务等待完成！成功: {len(results)} 个")
        
        return task_ids
        
    except Exception as e:
        print(f"❌ 多任务等待测试失败: {e}")
        return []


async def cleanup_tasks(task_ids):
    """清理测试任务"""
    if not task_ids:
        return
    
    print(f"\n🧹 清理 {len(task_ids)} 个测试任务...")
    
    generator = AsyncVideoGenerator()
    
    # 并发删除任务
    delete_tasks = [generator.delete_task(task_id) for task_id in task_ids]
    results = await asyncio.gather(*delete_tasks, return_exceptions=True)
    
    success_count = 0
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            print(f"⚠️ 删除任务失败 {task_ids[i]}: {result}")
        else:
            success_count += 1
    
    print(f"✅ 清理完成！成功删除 {success_count} 个任务")


async def cleanup_files():
    """清理测试文件"""
    files_to_remove = ["async_test_image.png"]
    
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"🧹 已删除文件: {file_path}")
            except Exception as e:
                print(f"⚠️ 删除文件失败 {file_path}: {e}")


async def main():
    """主测试函数"""
    print("🚀 AsyncVideoGenerator 异步视频生成器测试")
    print("=" * 70)
    
    # 检查环境变量
    required_env_vars = ["ARK_API_KEY", "ARK_BASE_URL", "ARK_VIDEO_MODEL"]
    missing_vars = [var for var in required_env_vars if not os.environ.get(var)]
    
    if missing_vars:
        print(f"❌ 缺少环境变量: {', '.join(missing_vars)}")
        print("请确保 .env 文件包含所有必需的环境变量")
        return
    
    print("✅ 环境变量检查通过")
    
    # 收集生成的任务ID
    all_task_ids = []
    
    try:
        # 运行各种测试
        await test_async_task_management()
        
        # 询问是否运行实际的视频生成测试
        print(f"\n❓ 是否要运行实际的异步视频生成测试？这将消耗API配额。(y/n): ", end="")
        # 在自动化环境中默认跳过
        user_input = "n"  # input().lower().strip()
        
        if user_input == "y":
            print("🎬 开始异步视频生成测试...")
            
            # 基本异步操作测试
            task_id = await test_basic_async_operations()
            if task_id:
                all_task_ids.append(task_id)
            
            # 异步图生视频测试
            task_id = await test_async_image_to_video()
            if task_id:
                all_task_ids.append(task_id)
            
            # 并发生成测试
            task_ids = await test_concurrent_generation()
            all_task_ids.extend(task_ids)
            
            # 多任务等待测试
            task_ids = await test_multiple_tasks_waiting()
            all_task_ids.extend(task_ids)
        else:
            print("⏭️ 跳过视频生成测试")
        
    finally:
        # 清理任务和文件
        await cleanup_tasks(all_task_ids)
        await cleanup_files()
    
    print("\n" + "=" * 70)
    print("🎉 AsyncVideoGenerator 测试完成！")
    print("💡 异步功能总结:")
    print("   ✅ 异步API调用（使用asyncio.to_thread）")
    print("   ✅ 异步文件处理（使用aiofiles）")
    print("   ✅ 异步等待（使用asyncio.sleep）")
    print("   ✅ 并发任务处理")
    print("   ✅ 批量视频生成")
    print("   ✅ 多任务并发等待")
    print("📖 查看 async_video_generator.py 获取完整API")


if __name__ == "__main__":
    asyncio.run(main())
