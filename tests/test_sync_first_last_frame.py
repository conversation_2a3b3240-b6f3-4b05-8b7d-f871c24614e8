#!/usr/bin/env python3
"""
测试同步VideoGenerator的首尾帧视频生成功能

这个脚本演示如何使用VideoGenerator的首尾帧视频生成功能
"""

import os
import asyncio
import aiohttp
from dotenv import load_dotenv
from video_generator import VideoGenerator, VideoGenerationConfig

# 确保加载环境变量
load_dotenv()


async def download_test_images():
    """下载测试用的首尾帧图片"""
    print("📥 下载测试图片...")

    # 使用火山引擎文档中的示例图片
    images = {
        "first_frame": {
            "url": "https://ark-project.tos-cn-beijing.volces.com/doc_image/wan_input_first_frame.png",
            "path": "sync_test_first_frame.png"
        },
        "last_frame": {
            "url": "https://ark-project.tos-cn-beijing.volces.com/doc_image/wan_input_last_frame.png",
            "path": "sync_test_last_frame.png"
        }
    }

    downloaded_paths = {}

    async with aiohttp.ClientSession() as session:
        for frame_type, info in images.items():
            if os.path.exists(info["path"]):
                print(f"✅ {frame_type} 图片已存在: {info['path']}")
                downloaded_paths[frame_type] = info["path"]
                continue

            try:
                async with session.get(info["url"]) as response:
                    response.raise_for_status()

                    with open(info["path"], 'wb') as f:
                        f.write(await response.read())

                print(f"✅ {frame_type} 图片已下载: {info['path']}")
                downloaded_paths[frame_type] = info["path"]

            except Exception as e:
                print(f"❌ 下载 {frame_type} 图片失败: {e}")
                downloaded_paths[frame_type] = None

    return downloaded_paths


async def test_sync_first_last_frame_generation():
    """测试同步首尾帧视频生成"""
    print("\n=== 测试同步首尾帧视频生成 ===")

    # 下载测试图片
    image_paths = await download_test_images()
    
    if not image_paths.get("first_frame") or not image_paths.get("last_frame"):
        print("❌ 无法获取测试图片，跳过首尾帧测试")
        return None
    
    generator = VideoGenerator()
    
    # 创建配置（注意：首尾帧功能可能需要特定的模型）
    config = VideoGenerationConfig(
        model="wan2-1-14b-flf2v-250417",  # 使用支持首尾帧的模型
        resolution="720p",
        duration=5,
        ratio="adaptive",
        framespersecond=24
    )
    
    try:
        print("🎬 开始生成首尾帧视频...")
        
        result = generator.generate_first_last_frame_video(
            prompt="CG动画风格，一只蓝色的小鸟从地面起飞，煽动翅膀。小鸟羽毛细腻，胸前有独特的花纹，背景是蓝天白云，阳光明媚。镜跟随小鸟向上移动，展现出小鸟飞翔的姿态和天空的广阔。近景，仰视视角。",
            first_frame_url=image_paths["first_frame"],
            last_frame_url=image_paths["last_frame"],
            config=config,
            verbose=True
        )
        
        print(f"✅ 同步首尾帧视频生成成功！")
        print(f"🆔 任务ID: {result.task_id}")
        print(f"📹 视频URL: {result.video_url}")
        
        return result.task_id
        
    except Exception as e:
        print(f"❌ 同步首尾帧视频生成失败: {e}")
        return None


async def test_sync_mixed_generation():
    """测试同步混合生成模式"""
    print("\n=== 测试同步混合生成模式 ===")

    # 下载测试图片
    image_paths = await download_test_images()
    
    generator = VideoGenerator()
    
    # 测试不同类型的生成任务
    generation_tasks = [
        {
            "name": "文生视频",
            "method": "generate_text_to_video",
            "args": {
                "prompt": "春天的樱花在微风中飞舞"
            }
        },
        {
            "name": "图生视频",
            "method": "generate_image_to_video", 
            "args": {
                "prompt": "女孩睁开眼睛，温柔地微笑",
                "image_url": image_paths.get("first_frame")
            }
        }
    ]
    
    # 如果有首尾帧图片，添加首尾帧任务
    if image_paths.get("first_frame") and image_paths.get("last_frame"):
        generation_tasks.append({
            "name": "首尾帧视频",
            "method": "generate_first_last_frame_video",
            "args": {
                "prompt": "小鸟从地面起飞到天空中飞翔",
                "first_frame_url": image_paths["first_frame"],
                "last_frame_url": image_paths["last_frame"],
                "config": VideoGenerationConfig(
                    model="wan2-1-14b-flf2v-250417",
                    resolution="720p",
                    duration=5,
                    ratio="adaptive"
                )
            }
        })
    
    task_ids = []
    
    try:
        print(f"🚀 开始同步混合模式生成 {len(generation_tasks)} 个视频...")
        
        for i, task in enumerate(generation_tasks):
            if task["args"].get("image_url") is None and "image_url" in task["args"]:
                print(f"⏭️ 跳过 {task['name']}（缺少图片）")
                continue
            
            print(f"\n🎬 生成 {task['name']} ({i+1}/{len(generation_tasks)})...")
            
            method = getattr(generator, task["method"])
            result = method(**task["args"], verbose=True)
            
            print(f"✅ {task['name']} 生成成功！")
            print(f"📹 视频URL: {result.video_url}")
            
            task_ids.append(result.task_id)
        
        print(f"\n✅ 同步混合模式生成完成！成功生成 {len(task_ids)} 个视频")
        
        return task_ids
        
    except Exception as e:
        print(f"❌ 同步混合模式生成失败: {e}")
        return task_ids


async def test_sync_image_processing():
    """测试同步图片处理功能"""
    print("\n=== 测试同步图片处理功能 ===")

    # 下载测试图片
    image_paths = await download_test_images()
    
    generator = VideoGenerator()
    
    try:
        # 测试网络URL处理
        network_url = "https://ark-project.tos-cn-beijing.volces.com/doc_image/wan_input_first_frame.png"
        print(f"🌐 处理网络URL: {network_url}")
        processed_network = generator._process_image_input(network_url)
        print(f"✅ 网络URL处理结果: {processed_network}")
        
        # 测试本地文件处理
        if image_paths.get("first_frame"):
            local_path = image_paths["first_frame"]
            print(f"\n📁 处理本地文件: {local_path}")
            processed_local = generator._process_image_input(local_path)
            print(f"✅ 本地文件处理成功，base64长度: {len(processed_local)}")
        
        print("✅ 同步图片处理功能测试完成！")
        
    except Exception as e:
        print(f"❌ 同步图片处理测试失败: {e}")


async def test_compatibility():
    """测试向后兼容性"""
    print("\n=== 测试向后兼容性 ===")

    # 下载测试图片
    image_paths = await download_test_images()
    
    generator = VideoGenerator()
    
    try:
        # 测试旧的image_url参数是否仍然工作
        if image_paths.get("first_frame"):
            print("🔄 测试旧接口兼容性...")
            
            result = generator.generate_video(
                prompt="女孩微笑，头发飞舞",
                image_url=image_paths["first_frame"],  # 使用旧的参数名
                verbose=True
            )
            
            print(f"✅ 向后兼容性测试成功！")
            print(f"🆔 任务ID: {result.task_id}")
            
            return result.task_id
        else:
            print("⏭️ 跳过兼容性测试（缺少图片）")
            return None
        
    except Exception as e:
        print(f"❌ 向后兼容性测试失败: {e}")
        return None


def cleanup_tasks(task_ids):
    """清理测试任务"""
    if not task_ids:
        return
    
    print(f"\n🧹 清理 {len(task_ids)} 个测试任务...")
    
    generator = VideoGenerator()
    
    success_count = 0
    for task_id in task_ids:
        try:
            generator.delete_task(task_id)
            success_count += 1
            print(f"✅ 已删除任务: {task_id}")
        except Exception as e:
            print(f"⚠️ 删除任务失败 {task_id}: {e}")
    
    print(f"✅ 清理完成！成功删除 {success_count} 个任务")


def cleanup_files():
    """清理测试文件"""
    files_to_remove = ["sync_test_first_frame.png", "sync_test_last_frame.png"]
    
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"🧹 已删除文件: {file_path}")
            except Exception as e:
                print(f"⚠️ 删除文件失败 {file_path}: {e}")


async def main():
    """主测试函数"""
    print("🎬 同步VideoGenerator首尾帧功能测试")
    print("=" * 70)

    # 检查环境变量
    required_env_vars = ["ARK_API_KEY", "ARK_BASE_URL"]
    missing_vars = [var for var in required_env_vars if not os.environ.get(var)]

    if missing_vars:
        print(f"❌ 缺少环境变量: {', '.join(missing_vars)}")
        print("请确保 .env 文件包含所有必需的环境变量")
        return

    print("✅ 环境变量检查通过")

    # 收集生成的任务ID
    all_task_ids = []

    try:
        # 运行图片处理测试
        await test_sync_image_processing()

        # 询问是否运行实际的视频生成测试
        print(f"\n❓ 是否要运行实际的同步首尾帧视频生成测试？这将消耗API配额。(y/n): ", end="")
        # 在自动化环境中默认跳过
        user_input = "n"  # input().lower().strip()

        if user_input == "y":
            print("🎬 开始同步首尾帧视频生成测试...")

            # 首尾帧生成测试
            task_id = await test_sync_first_last_frame_generation()
            if task_id:
                all_task_ids.append(task_id)

            # 混合模式生成测试
            task_ids = await test_sync_mixed_generation()
            all_task_ids.extend(task_ids)

            # 向后兼容性测试
            task_id = await test_compatibility()
            if task_id:
                all_task_ids.append(task_id)
        else:
            print("⏭️ 跳过视频生成测试")

    finally:
        # 清理任务和文件
        cleanup_tasks(all_task_ids)
        cleanup_files()

    print("\n" + "=" * 70)
    print("🎉 同步VideoGenerator首尾帧功能测试完成！")
    print("💡 新功能总结:")
    print("   ✅ 同步首尾帧视频生成")
    print("   ✅ 混合生成模式（文生视频 + 图生视频 + 首尾帧视频）")
    print("   ✅ 向后兼容性（image_url参数）")
    print("   ✅ 本地图片处理")
    print("📖 查看 video_generator.py 获取完整API")


if __name__ == "__main__":
    asyncio.run(main())
