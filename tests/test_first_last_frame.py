#!/usr/bin/env python3
"""
测试首尾帧视频生成功能

这个脚本演示如何使用AsyncVideoGenerator的首尾帧视频生成功能
"""

import asyncio
import os
import aiohttp
from dotenv import load_dotenv
from async_video_generator import AsyncVideoGenerator
from video_generator import VideoGenerationConfig

# 确保加载环境变量
load_dotenv()


async def download_test_images():
    """下载测试用的首尾帧图片"""
    print("📥 下载测试图片...")
    
    # 使用火山引擎文档中的示例图片
    images = {
        "first_frame": {
            "url": "https://ark-project.tos-cn-beijing.volces.com/doc_image/wan_input_first_frame.png",
            "path": "test_first_frame.png"
        },
        "last_frame": {
            "url": "https://ark-project.tos-cn-beijing.volces.com/doc_image/wan_input_last_frame.png", 
            "path": "test_last_frame.png"
        }
    }
    
    downloaded_paths = {}
    
    async with aiohttp.ClientSession() as session:
        for frame_type, info in images.items():
            if os.path.exists(info["path"]):
                print(f"✅ {frame_type} 图片已存在: {info['path']}")
                downloaded_paths[frame_type] = info["path"]
                continue
            
            try:
                async with session.get(info["url"]) as response:
                    response.raise_for_status()
                    
                    with open(info["path"], 'wb') as f:
                        f.write(await response.read())
                
                print(f"✅ {frame_type} 图片已下载: {info['path']}")
                downloaded_paths[frame_type] = info["path"]
                
            except Exception as e:
                print(f"❌ 下载 {frame_type} 图片失败: {e}")
                downloaded_paths[frame_type] = None
    
    return downloaded_paths


async def test_first_last_frame_generation():
    """测试首尾帧视频生成"""
    print("\n=== 测试首尾帧视频生成 ===")
    
    # 下载测试图片
    image_paths = await download_test_images()
    
    if not image_paths.get("first_frame") or not image_paths.get("last_frame"):
        print("❌ 无法获取测试图片，跳过首尾帧测试")
        return None
    
    generator = AsyncVideoGenerator()
    
    # 创建配置（注意：首尾帧功能可能需要特定的模型）
    config = VideoGenerationConfig(
        model="wan2-1-14b-flf2v-250417",  # 使用支持首尾帧的模型
        resolution="720p",
        duration=5,
        ratio="adaptive",
        framespersecond=24
    )
    
    try:
        print("🎬 开始生成首尾帧视频...")
        
        result = await generator.generate_first_last_frame_video(
            prompt="CG动画风格，一只蓝色的小鸟从地面起飞，煽动翅膀。小鸟羽毛细腻，胸前有独特的花纹，背景是蓝天白云，阳光明媚。镜跟随小鸟向上移动，展现出小鸟飞翔的姿态和天空的广阔。近景，仰视视角。",
            first_frame_url=image_paths["first_frame"],
            last_frame_url=image_paths["last_frame"],
            config=config,
            verbose=True
        )
        
        print(f"✅ 首尾帧视频生成成功！")
        print(f"🆔 任务ID: {result.task_id}")
        print(f"📹 视频URL: {result.video_url}")
        
        return result.task_id
        
    except Exception as e:
        print(f"❌ 首尾帧视频生成失败: {e}")
        return None


async def test_mixed_generation_modes():
    """测试混合生成模式"""
    print("\n=== 测试混合生成模式 ===")
    
    # 下载测试图片
    image_paths = await download_test_images()
    
    generator = AsyncVideoGenerator()
    
    # 准备不同类型的生成任务
    generation_tasks = [
        {
            "name": "文生视频",
            "prompt": "春天的樱花在微风中飞舞",
            "config": None,
            "image_url": None,
            "first_frame_url": None,
            "last_frame_url": None
        },
        {
            "name": "图生视频",
            "prompt": "女孩睁开眼睛，温柔地微笑",
            "config": None,
            "image_url": image_paths.get("first_frame"),
            "first_frame_url": None,
            "last_frame_url": None
        }
    ]
    
    # 如果有首尾帧图片，添加首尾帧任务
    if image_paths.get("first_frame") and image_paths.get("last_frame"):
        generation_tasks.append({
            "name": "首尾帧视频",
            "prompt": "小鸟从地面起飞到天空中飞翔",
            "config": VideoGenerationConfig(
                model="wan2-1-14b-flf2v-250417",
                resolution="720p",
                duration=5,
                ratio="adaptive"
            ),
            "image_url": None,
            "first_frame_url": image_paths["first_frame"],
            "last_frame_url": image_paths["last_frame"]
        })
    
    try:
        print(f"🚀 开始混合模式生成 {len(generation_tasks)} 个视频...")
        
        # 提取参数列表
        prompts = [task["prompt"] for task in generation_tasks]
        configs = [task["config"] for task in generation_tasks]
        image_urls = [task["image_url"] for task in generation_tasks]
        first_frame_urls = [task["first_frame_url"] for task in generation_tasks]
        last_frame_urls = [task["last_frame_url"] for task in generation_tasks]
        
        # 批量生成
        results = await generator.generate_videos_batch(
            prompts=prompts,
            configs=configs,
            image_urls=image_urls,
            first_frame_urls=first_frame_urls,
            last_frame_urls=last_frame_urls,
            max_concurrent=2,
            poll_interval=10,
            max_wait_time=300,  # 5分钟
            verbose=True
        )
        
        print(f"✅ 混合模式生成完成！成功生成 {len(results)} 个视频")
        
        # 显示结果
        for i, (task, result) in enumerate(zip(generation_tasks, results)):
            print(f"  {i+1}. {task['name']}: {result.video_url}")
        
        task_ids = [result.task_id for result in results]
        return task_ids
        
    except Exception as e:
        print(f"❌ 混合模式生成失败: {e}")
        return []


async def test_image_processing():
    """测试图片处理功能"""
    print("\n=== 测试图片处理功能 ===")
    
    # 下载测试图片
    image_paths = await download_test_images()
    
    generator = AsyncVideoGenerator()
    
    try:
        # 测试网络URL处理
        network_url = "https://ark-project.tos-cn-beijing.volces.com/doc_image/wan_input_first_frame.png"
        print(f"🌐 处理网络URL: {network_url}")
        processed_network = await generator._process_image_input_async(network_url)
        print(f"✅ 网络URL处理结果: {processed_network}")
        
        # 测试本地文件处理
        if image_paths.get("first_frame"):
            local_path = image_paths["first_frame"]
            print(f"\n📁 处理本地文件: {local_path}")
            processed_local = await generator._process_image_input_async(local_path)
            print(f"✅ 本地文件处理成功，base64长度: {len(processed_local)}")
        
        print("✅ 图片处理功能测试完成！")
        
    except Exception as e:
        print(f"❌ 图片处理测试失败: {e}")


async def cleanup_tasks(task_ids):
    """清理测试任务"""
    if not task_ids:
        return
    
    print(f"\n🧹 清理 {len(task_ids)} 个测试任务...")
    
    generator = AsyncVideoGenerator()
    
    # 并发删除任务
    delete_tasks = [generator.delete_task(task_id) for task_id in task_ids]
    results = await asyncio.gather(*delete_tasks, return_exceptions=True)
    
    success_count = 0
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            print(f"⚠️ 删除任务失败 {task_ids[i]}: {result}")
        else:
            success_count += 1
    
    print(f"✅ 清理完成！成功删除 {success_count} 个任务")


async def cleanup_files():
    """清理测试文件"""
    files_to_remove = ["test_first_frame.png", "test_last_frame.png"]
    
    for file_path in files_to_remove:
        if os.path.exists(file_path):
            try:
                os.remove(file_path)
                print(f"🧹 已删除文件: {file_path}")
            except Exception as e:
                print(f"⚠️ 删除文件失败 {file_path}: {e}")


async def main():
    """主测试函数"""
    print("🎬 首尾帧视频生成功能测试")
    print("=" * 70)
    
    # 检查环境变量
    required_env_vars = ["ARK_API_KEY", "ARK_BASE_URL"]
    missing_vars = [var for var in required_env_vars if not os.environ.get(var)]
    
    if missing_vars:
        print(f"❌ 缺少环境变量: {', '.join(missing_vars)}")
        print("请确保 .env 文件包含所有必需的环境变量")
        return
    
    print("✅ 环境变量检查通过")
    
    # 收集生成的任务ID
    all_task_ids = []
    
    try:
        # 运行图片处理测试
        await test_image_processing()
        
        # 询问是否运行实际的视频生成测试
        print(f"\n❓ 是否要运行实际的首尾帧视频生成测试？这将消耗API配额。(y/n): ", end="")
        # 在自动化环境中默认跳过
        user_input = "n"  # input().lower().strip()
        
        if user_input == "y":
            print("🎬 开始首尾帧视频生成测试...")
            
            # 首尾帧生成测试
            task_id = await test_first_last_frame_generation()
            if task_id:
                all_task_ids.append(task_id)
            
            # 混合模式生成测试
            task_ids = await test_mixed_generation_modes()
            all_task_ids.extend(task_ids)
        else:
            print("⏭️ 跳过视频生成测试")
        
    finally:
        # 清理任务和文件
        await cleanup_tasks(all_task_ids)
        await cleanup_files()
    
    print("\n" + "=" * 70)
    print("🎉 首尾帧视频生成功能测试完成！")
    print("💡 新功能总结:")
    print("   ✅ 首尾帧视频生成")
    print("   ✅ 混合生成模式（文生视频 + 图生视频 + 首尾帧视频）")
    print("   ✅ 批量生成支持首尾帧")
    print("   ✅ 兼容旧接口（image_url）")
    print("📖 查看 async_video_generator.py 获取完整API")


if __name__ == "__main__":
    asyncio.run(main())
