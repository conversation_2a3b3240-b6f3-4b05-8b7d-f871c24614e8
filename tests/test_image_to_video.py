#!/usr/bin/env python3
"""
测试图生视频功能

这个脚本演示如何使用VideoGenerator类的图生视频功能
"""

from video_generator import VideoGenerator, VideoGenerationConfig
import os
from dotenv import load_dotenv

# 确保加载环境变量
load_dotenv()


def test_image_to_video():
    """测试图生视频功能"""
    print("=== 测试图生视频功能 ===")
    
    # 使用火山引擎文档中的示例图片
    test_image_url = "https://ark-project.tos-cn-beijing.volces.com/doc_image/i2v_foxrgirl.png"
    
    generator = VideoGenerator()
    
    # 创建配置
    config = VideoGenerationConfig(
        model=os.environ.get("ARK_VIDEO_MODEL"),
        resolution="1080p",
        duration=5,
        ratio="adaptive",  # 使用adaptive比例，某些模型只支持这个
        framespersecond=24
    )
    
    try:
        print(f"📸 使用图片: {test_image_url}")
        print("🎬 开始生成图生视频...")
        
        # 使用专用的图生视频方法
        result = generator.generate_image_to_video(
            prompt="女孩抱着狐狸，女孩睁开眼，温柔地看向镜头，狐狸友善地抱着，镜头缓缓拉出，女孩的头发被风吹动",
            image_url=test_image_url,
            config=config,
            verbose=True
        )
        
        print(f"✅ 图生视频生成成功！")
        print(f"🆔 任务ID: {result.task_id}")
        print(f"📹 视频URL: {result.video_url}")
        print(f"📊 使用情况: {result.usage}")
        
        return result.task_id
        
    except Exception as e:
        print(f"❌ 图生视频生成失败: {e}")
        return None


def test_text_to_video():
    """测试文生视频功能（对比）"""
    print("\n=== 测试文生视频功能（对比） ===")
    
    generator = VideoGenerator()
    
    # 创建配置
    config = VideoGenerationConfig(
        model=os.environ.get("ARK_VIDEO_MODEL"),
        resolution="1080p",
        duration=5,
        ratio="adaptive",  # 使用adaptive比例
        framespersecond=24
    )
    
    try:
        print("🎬 开始生成文生视频...")
        
        # 使用专用的文生视频方法
        result = generator.generate_text_to_video(
            prompt="一个可爱的女孩抱着一只小狐狸，在温暖的阳光下微笑",
            config=config,
            verbose=True
        )
        
        print(f"✅ 文生视频生成成功！")
        print(f"🆔 任务ID: {result.task_id}")
        print(f"📹 视频URL: {result.video_url}")
        
        return result.task_id
        
    except Exception as e:
        print(f"❌ 文生视频生成失败: {e}")
        return None


def test_create_task_with_image():
    """测试使用create_video_task方法创建图生视频任务"""
    print("\n=== 测试create_video_task方法（图生视频） ===")
    
    generator = VideoGenerator()
    test_image_url = "https://ark-project.tos-cn-beijing.volces.com/doc_image/i2v_foxrgirl.png"
    
    try:
        # 创建任务但不等待完成
        result = generator.create_video_task(
            prompt="女孩眨眼，狐狸摇尾巴，背景中的花朵轻轻摇摆",
            image_url=test_image_url
        )
        
        print(f"✅ 图生视频任务创建成功！")
        print(f"🆔 任务ID: {result.task_id}")
        print(f"📊 初始状态: {result.status}")
        
        # 检查任务状态
        status = generator.get_task_status(result.task_id)
        print(f"📊 当前状态: {status.status}")
        
        # 等待完成
        if status.status in ["queued", "running"]:
            print("⏳ 等待任务完成...")
            final_result = generator.wait_for_completion(
                result.task_id,
                poll_interval=10,
                max_wait_time=120,  # 2分钟
                verbose=True
            )
            
            if final_result.video_url:
                print(f"✅ 任务完成！视频URL: {final_result.video_url}")
        
        return result.task_id
        
    except Exception as e:
        print(f"❌ 创建图生视频任务失败: {e}")
        return None


def test_different_image_urls():
    """测试不同的图片URL"""
    print("\n=== 测试不同图片URL ===")
    
    generator = VideoGenerator()
    
    # 测试图片列表（这些是示例URL，实际使用时需要替换为有效的图片URL）
    test_images = [
        {
            "url": "https://ark-project.tos-cn-beijing.volces.com/doc_image/i2v_foxrgirl.png",
            "prompt": "女孩微笑，狐狸摇尾巴",
            "description": "火山引擎官方示例图片"
        }
    ]
    
    for i, image_info in enumerate(test_images):
        print(f"\n📸 测试图片 {i+1}: {image_info['description']}")
        print(f"🔗 URL: {image_info['url']}")
        
        try:
            # 只创建任务，不等待完成（节省时间）
            result = generator.create_video_task(
                prompt=image_info['prompt'],
                image_url=image_info['url']
            )
            
            print(f"✅ 任务创建成功: {result.task_id}")
            
        except Exception as e:
            print(f"❌ 任务创建失败: {e}")


def main():
    """主测试函数"""
    print("🎬 图生视频功能测试")
    print("=" * 60)
    
    # 检查环境变量
    required_env_vars = ["ARK_API_KEY", "ARK_BASE_URL", "ARK_VIDEO_MODEL"]
    missing_vars = [var for var in required_env_vars if not os.environ.get(var)]
    
    if missing_vars:
        print(f"❌ 缺少环境变量: {', '.join(missing_vars)}")
        print("请确保 .env 文件包含所有必需的环境变量")
        return
    
    print("✅ 环境变量检查通过")
    
    # 收集生成的任务ID
    task_ids = []
    
    # 运行测试
    print("\n🔍 开始测试图生视频功能...")
    
    # 测试图生视频
    task_id = test_image_to_video()
    if task_id:
        task_ids.append(task_id)
    
    # 测试文生视频（对比）
    task_id = test_text_to_video()
    if task_id:
        task_ids.append(task_id)
    
    # 测试create_video_task方法
    task_id = test_create_task_with_image()
    if task_id:
        task_ids.append(task_id)
    
    # 测试不同图片URL
    test_different_image_urls()
    
    # 清理测试任务
    if task_ids:
        print(f"\n🧹 清理测试任务...")
        generator = VideoGenerator()
        for task_id in task_ids:
            try:
                generator.delete_task(task_id)
                print(f"✅ 已删除任务: {task_id}")
            except Exception as e:
                print(f"⚠️ 删除任务失败 {task_id}: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 图生视频功能测试完成！")
    print("💡 提示: 图生视频需要提供有效的图片URL")
    print("📖 提示: 查看 README.md 获取更多使用说明")


if __name__ == "__main__":
    main()
